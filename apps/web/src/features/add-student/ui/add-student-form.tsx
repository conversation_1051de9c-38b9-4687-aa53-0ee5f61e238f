'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { routes } from '@lilypad/shared/routes';
import { Button } from '@lilypad/ui/components/button';
import { Form } from '@lilypad/ui/components/form';
import { toast } from '@lilypad/ui/components/sonner';
import { defineStepper, useButtonLabel } from '@lilypad/ui/components/stepper';
import { ArrowRightIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useCallback, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useAddStudent } from '../model/hooks/use-add-student';
import { useStudentFormNavigation } from '../model/hooks/use-student-form-navigation';
import type { AddStudentFormData } from '../model/schemas';
import { addStudentFormSchema } from '../model/schemas';
import {
  STUDENT_FORM_STEPS,
  StudentFormStepId,
} from '../model/step-definitions';
import {
  createDefaultFormValues,
  transformFormDataToAPI,
} from '../model/transformers';
import {
  BasicInfoStep,
  CaseStep,
  ContactInfoStep,
  DocumentsStep,
  ParentsStep,
  ReviewStep,
} from './steps';

const { Stepper, useStepper } = defineStepper(...STUDENT_FORM_STEPS);

export function AddStudentForm() {
  const router = useRouter();
  const [resetKey, _setResetKey] = useState(0);

  const form = useForm<AddStudentFormData>({
    resolver: zodResolver(addStudentFormSchema),
    defaultValues: createDefaultFormValues(),
    mode: 'onChange',
  });

  const stepperMethods = useStepper();
  const navigation = useStudentFormNavigation(form.watch());

  const { addStudent, isAdding } = useAddStudent({
    onSuccess: () => {
      router.push(routes.app.students.Index);
    },
  });

  const onSubmit = useCallback(
    async (data: AddStudentFormData) => {
      try {
        const apiData = await transformFormDataToAPI(data);
        addStudent(apiData);
      } catch (error) {
        console.error('Form submission error:', error);
      }
    },
    [addStudent]
  );

  const handleNext = useCallback(() => {
    const currentStepId = stepperMethods.current?.id;
    const canProceed = navigation.canProceedFromStep(currentStepId);

    if (canProceed.allowed) {
      stepperMethods.next();
    }
  }, [stepperMethods, navigation]);

  const handleStepClick = useCallback(
    (stepId: StudentFormStepId) => {
      const targetStep = stepId;
      const canNavigate = navigation.canNavigateToStep(targetStep);

      if (canNavigate.allowed) {
        stepperMethods.goTo(stepId);
      } else {
        toast.error(canNavigate.reason || 'Cannot navigate to this step yet');
      }
    },
    [stepperMethods, navigation]
  );

  const getCurrentStepValidation = useCallback(() => {
    const currentStepId = stepperMethods.current?.id;
    return navigation.canProceedFromStep(currentStepId);
  }, [stepperMethods, navigation]);

  const isCurrentStepValid = useCallback(() => {
    return getCurrentStepValidation().allowed;
  }, [getCurrentStepValidation]);

  const buttonLabel = useButtonLabel(stepperMethods, 'Create Student');

  return (
    <div className="flex h-full flex-col">
      <Form {...form}>
        <form
          className="flex h-full flex-col"
          onSubmit={form.handleSubmit(onSubmit)}
        >
          <Stepper.Provider key={resetKey} variant="horizontal">
            {/* Stepper Navigation */}
            <div className="scrollarea flex-shrink-0 overflow-x-auto border-b bg-background px-6 py-4">
              <Stepper.Navigation className="flex md:justify-center">
                {stepperMethods.all.map((step) => {
                  const stepId = step.id as StudentFormStepId;
                  const canNavigate = navigation.canNavigateToStep(stepId);
                  const isCompleted = navigation.isStepCompleted(stepId);
                  const isCurrent = stepperMethods.current?.id === step.id;

                  return (
                    <Stepper.Step
                      key={step.id}
                      of={step.id}
                      icon={<step.icon className='size-4' />}
                      disabled={!canNavigate.allowed}
                      onClick={() => handleStepClick(step.id)}
                      className="flex-1 "
                      data-completed={isCompleted}
                      data-current={isCurrent}
                    >
                      <Stepper.Title className="whitespace-nowrap font-medium text-sm">
                        {step.title}
                      </Stepper.Title>
                      <Stepper.Description className="whitespace-nowrap text-muted-foreground text-xs">
                        {step.description}
                      </Stepper.Description>
                    </Stepper.Step>
                  );
                })}
              </Stepper.Navigation>
            </div>

            {/* Step Content */}
            <div className="min-h-0 flex-1 overflow-hidden md:mx-auto md:max-w-3xl">
              <Stepper.Panel className="h-full overflow-y-auto p-6">
                {stepperMethods.switch({
                  [StudentFormStepId.BASIC_INFO]: () => <BasicInfoStep />,
                  [StudentFormStepId.CONTACT_INFO]: () => <ContactInfoStep />,
                  [StudentFormStepId.PARENTS]: () => <ParentsStep />,
                  [StudentFormStepId.DOCUMENTS]: () => <DocumentsStep />,
                  [StudentFormStepId.CASE]: () => <CaseStep />,
                  [StudentFormStepId.REVIEW]: () => (
                    <ReviewStep onEditStep={(stepId) => stepperMethods.goTo(stepId)} />
                  ),
                })}
              </Stepper.Panel>
            </div>

            {/* Form Controls */}
            <div className="flex-shrink-0 rounded-md border bg-secondary p-4 md:mx-auto md:max-w-3xl">
              <Stepper.Controls className="flex justify-between">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => router.push(routes.app.students.Index)}
                  disabled={isAdding}
                >
                  Cancel
                </Button>
                <div className="flex flex-col items-end gap-2">
                  {/* Validation feedback */}
                  {!isCurrentStepValid() && (
                    <div className="text-destructive text-sm">
                      {getCurrentStepValidation().reason || 'Please complete required fields'}
                    </div>
                  )}
                  <div className="flex gap-2">
                    {!stepperMethods.isFirst && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={stepperMethods.prev}
                        disabled={isAdding}
                      >
                        Previous
                      </Button>
                    )}
                    <Button
                      type={stepperMethods.isLast ? 'submit' : 'button'}
                      size="sm"
                      onClick={stepperMethods.isLast ? undefined : handleNext}
                      disabled={!isCurrentStepValid() || isAdding}
                      loading={stepperMethods.isLast && isAdding}
                    >
                      {buttonLabel}
                      {!stepperMethods.isLast && (
                        <ArrowRightIcon className="ml-2 size-4" />
                      )}
                    </Button>
                  </div>
                </div>
              </Stepper.Controls>
            </div>
          </Stepper.Provider>
        </form>
      </Form>
    </div >
  );
}