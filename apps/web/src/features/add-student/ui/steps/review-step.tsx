'use client';

import { DocumentCategoryEnum, ParentRelationshipEnumMap } from '@lilypad/db/enums';
import { Badge } from '@lilypad/ui/components/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@lilypad/ui/components/card';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import { If } from '@lilypad/ui/components/if';
import { Skeleton } from '@lilypad/ui/components/skeleton';
import { formatDate } from '@lilypad/ui/lib/utils';
import {
  CalendarIcon,
  EditIcon,
  FileIcon,
  MailIcon,
  PhoneIcon,
  SchoolIcon,
  UserIcon,
} from 'lucide-react';
import dynamic from 'next/dynamic';
import React from 'react';
import { useFormContext } from 'react-hook-form';
import { calculateAge } from '@/shared/lib/utils';
import { SchoolGradeBadge } from '@/shared/ui/students/school-grade-badge';
import { StudentGenderBadge } from '@/shared/ui/students/student-gender-badge';
import type { AddStudentFormData } from '../../model/schemas';
import { StudentFormStepId } from '../../model/step-definitions';

// Dynamically import the editor to avoid SSR issues
const LilypadEditor = dynamic(
  () => import('@lilypad/editor').then((mod) => mod.LilypadEditor),
  {
    ssr: false,
    loading: () => (
      <div className="space-y-2">
        <Skeleton className="h-12 w-full" />
        <Skeleton className="h-64 w-full" />
      </div>
    ),
  }
);

interface ReviewStepProps {
  onEditStep?: (stepId: StudentFormStepId) => void;
}

export function ReviewStep({ onEditStep }: ReviewStepProps) {
  const form = useFormContext<AddStudentFormData>();
  const formData = form.watch();

  const age = React.useMemo(() => {
    if (!formData.dateOfBirth) { return 0; }
    return calculateAge(formData.dateOfBirth.toISOString());
  }, [formData.dateOfBirth]);

  const hasConsentForm = React.useMemo(() => {
    return formData.documents?.some(doc => doc.category === DocumentCategoryEnum.CONSENT_FORM);
  }, [formData.documents]);

  return (
    <div className="space-y-6">
      <div>
        <h2 className="font-semibold text-lg">Review & Submit</h2>
        <p className="text-muted-foreground text-sm">
          Review all the information below and make any necessary changes before submitting.
        </p>
      </div>

      {/* Basic Information */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-base">Basic Information</CardTitle>
          <button
            type="button"
            onClick={() => onEditStep?.(StudentFormStepId.BASIC_INFO)}
            className="flex items-center gap-1 text-muted-foreground text-sm hover:text-primary"
          >
            <EditIcon className="size-3" />
            Edit
          </button>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Full Name:</span>
              <p className="font-medium">
                {formData.firstName} {formData.middleName} {formData.lastName}
              </p>
            </div>
            <If condition={formData.preferredName}>
              <div>
                <span className="text-muted-foreground">Preferred Name:</span>
                <p className="font-medium">{formData.preferredName}</p>
              </div>
            </If>
            <div>
              <span className="text-muted-foreground">Student ID:</span>
              <p className="font-medium">{formData.studentIdNumber}</p>
            </div>
            <div>
              <span className="text-muted-foreground">Gender:</span>
              <div className="mt-1">
                {formData.gender && <StudentGenderBadge gender={formData.gender} />}
              </div>
            </div>
            <div>
              <span className="text-muted-foreground">Date of Birth:</span>
              <div className="flex items-center gap-2">
                <CalendarIcon className="size-3 text-muted-foreground" />
                <p className="font-medium">
                  {formData.dateOfBirth && formatDate(formData.dateOfBirth)} ({age} years old)
                </p>
              </div>
            </div>
            <div>
              <span className="text-muted-foreground">Date of Consent:</span>
              <div className="flex items-center gap-2">
                <CalendarIcon className="size-3 text-muted-foreground" />
                <p className="font-medium">
                  {formData.dateOfConsent && formatDate(formData.dateOfConsent)}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contact & School Information */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-base">Contact & School Information</CardTitle>
          <button
            type="button"
            onClick={() => onEditStep?.(StudentFormStepId.CONTACT_INFO)}
            className="flex items-center gap-1 text-muted-foreground text-sm hover:text-primary"
          >
            <EditIcon className="size-3" />
            Edit
          </button>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <If condition={formData.primarySchoolId}>
              <div>
                <span className="text-muted-foreground">Primary School:</span>
                <div className="flex items-center gap-2">
                  <SchoolIcon className="size-3 text-muted-foreground" />
                  <p className="font-medium">School Selected</p>
                </div>
              </div>
            </If>
            <If condition={formData.grade}>
              <div>
                <span className="text-muted-foreground">Grade:</span>
                <div className="mt-1">
                  <SchoolGradeBadge grade={formData.grade} />
                </div>
              </div>
            </If>
            <If condition={formData.languageIds?.length}>
              <div className="col-span-2">
                <span className="text-muted-foreground">Languages:</span>
                <p className="font-medium">{formData.languageIds?.length} language(s) selected</p>
              </div>
            </If>
            <If condition={formData.specialNeedsIndicator}>
              <div className="col-span-2">
                <Badge variant="outline">Special Needs Student</Badge>
              </div>
            </If>
          </div>
        </CardContent>
      </Card>

      {/* Parents & Guardians */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-base">Parents & Guardians</CardTitle>
          <button
            type="button"
            onClick={() => onEditStep?.(StudentFormStepId.PARENTS)}
            className="flex items-center gap-1 text-muted-foreground text-sm hover:text-primary"
          >
            <EditIcon className="size-3" />
            Edit
          </button>
        </CardHeader>
        <CardContent>
          <If condition={formData.parents?.length}>
            <div className="space-y-3">
              {formData.parents?.map((parent, index) => (
                <div key={`parent-${parent.firstName}-${parent.lastName}-${index}`} className="flex items-start gap-3 rounded-lg border p-3">
                  <UserIcon className="mt-0.5 size-4 text-muted-foreground" />
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center gap-2">
                      <p className="font-medium">
                        {parent.firstName} {parent.middleName} {parent.lastName}
                      </p>
                      <Badge variant="outline">
                        {ParentRelationshipEnumMap[parent.relationshipType]}
                      </Badge>
                      <If condition={parent.isPrimaryContact}>
                        <Badge className="border-primary bg-primary/10 text-primary" variant="outline">
                          Primary Contact
                        </Badge>
                      </If>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <If condition={parent.primaryEmail}>
                        <div className="flex items-center gap-2 text-muted-foreground">
                          <MailIcon className="size-3" />
                          <span>{parent.primaryEmail}</span>
                        </div>
                      </If>
                      <If condition={parent.primaryPhone}>
                        <div className="flex items-center gap-2 text-muted-foreground">
                          <PhoneIcon className="size-3" />
                          <span>{parent.primaryPhone}</span>
                        </div>
                      </If>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </If>
        </CardContent>
      </Card>

      {/* Documents */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-base">Documents</CardTitle>
          <button
            type="button"
            onClick={() => onEditStep?.(StudentFormStepId.DOCUMENTS)}
            className="flex items-center gap-1 text-muted-foreground text-sm hover:text-primary"
          >
            <EditIcon className="size-3" />
            Edit
          </button>
        </CardHeader>
        <CardContent>
          <If
            condition={formData.documents?.length}
            fallback={
              <p className="text-muted-foreground text-sm">No documents uploaded</p>
            }
          >
            <div className="space-y-2">
              {formData.documents?.map((doc, index) => {
                const fileName = doc.file instanceof File ? doc.file.name : doc.file.name;
                return (
                  <div key={`document-${fileName}-${index}`} className="flex items-center gap-3 text-sm">
                    <FileIcon className="size-4 text-muted-foreground" />
                    <span className="flex-1">{fileName}</span>
                    <Badge variant="outline">{doc.category}</Badge>
                    <If condition={doc.category === DocumentCategoryEnum.CONSENT_FORM}>
                      <Badge className="border-green-200 bg-green-100 text-green-800">
                        Consent Form
                      </Badge>
                    </If>
                  </div>
                );
              })}
            </div>
          </If>
        </CardContent>
      </Card>

      {/* Case Information */}
      <If condition={formData.caseInfo?.shouldCreateCase && hasConsentForm}>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="text-base">Case Information</CardTitle>
            <button
              type="button"
              onClick={() => onEditStep?.(StudentFormStepId.CASE)}
              className="flex items-center gap-1 text-muted-foreground text-sm hover:text-primary"
            >
              <EditIcon className="size-3" />
              Edit
            </button>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Case Type:</span>
                <p className="font-medium">{formData.caseInfo?.caseType}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Priority:</span>
                <p className="font-medium">{formData.caseInfo?.priority}</p>
              </div>
              <If condition={formData.caseInfo?.referralDate}>
                <div>
                  <span className="text-muted-foreground">Referral Date:</span>
                  <p className="font-medium">
                    {formData.caseInfo?.referralDate && formatDate(formData.caseInfo.referralDate)}
                  </p>
                </div>
              </If>
            </div>
          </CardContent>
        </Card>
      </If>

      {/* Notes */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Notes</CardTitle>
        </CardHeader>
        <CardContent>
          <FormField
            control={form.control}
            name="notes.content"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="sr-only">Notes</FormLabel>
                <FormControl>
                  <LilypadEditor
                    className="min-h-[200px] border"
                    editorContentClassName="p-4"
                    onChange={field.onChange}
                    placeholder="Enter any additional notes about the student..."
                    value={field.value}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>
    </div>
  );
}