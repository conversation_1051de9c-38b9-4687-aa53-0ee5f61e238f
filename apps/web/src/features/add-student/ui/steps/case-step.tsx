'use client';

import {
  CaseP<PERSON>rityEnum,
  CaseTypeEnum,
  DocumentCategoryEnum,
} from '@lilypad/db/enums';
import { Button } from '@lilypad/ui/components/button';
import { Calendar } from '@lilypad/ui/components/calendar';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import { If } from '@lilypad/ui/components/if';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@lilypad/ui/components/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@lilypad/ui/components/select';
import { Switch } from '@lilypad/ui/components/switch';
import { cn } from '@lilypad/ui/lib/utils';
import { format } from 'date-fns';
import { CalendarIcon, ClockIcon, FileTextIcon, InfoIcon } from 'lucide-react';
import { useFormContext } from 'react-hook-form';
import type { AddStudentFormData } from '../../model/schemas';

export function CaseStep() {
  const form = useFormContext<AddStudentFormData>();
  const shouldCreateCase = form.watch('caseInfo.shouldCreateCase');
  const documents = form.watch('documents') || [];

  // Check if there are any consent form documents uploaded
  const hasConsentForm = documents.some(
    (doc) => doc.category === DocumentCategoryEnum.CONSENT_FORM
  );

  const handleSwitchChange = (checked: boolean) => {
    // Only allow turning on if there's a consent form
    if (checked && !hasConsentForm) {
      return;
    }
    form.setValue('caseInfo.shouldCreateCase', checked);
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="font-semibold text-lg">Case Creation</h2>
        <p className="text-muted-foreground text-sm">
          Optionally create a case for this student to track their evaluation progress. A consent form is required to enable case creation.
        </p>
      </div>

      <FormField
        control={form.control}
        name="caseInfo.shouldCreateCase"
        render={({ field }) => (
          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
            <div className="space-y-0.5">
              <FormLabel className="text-base">Create Case</FormLabel>
              <FormDescription>
                Automatically create a case for this student to track their
                evaluation progress.
              </FormDescription>
            </div>
            <FormControl>
              <Switch
                checked={field.value}
                disabled={!hasConsentForm}
                onCheckedChange={handleSwitchChange}
              />
            </FormControl>
          </FormItem>
        )}
      />

      <If condition={!hasConsentForm}>
        <div className="flex items-start gap-3 rounded-lg border border-dashed bg-muted/50 p-4 text-muted-foreground">
          <InfoIcon className="mt-0.5 size-5 flex-shrink-0" />
          <div>
            <p className="font-medium text-sm">Consent Form Required</p>
            <p className="mt-1 text-xs">
              A consent form must be uploaded in the Documents step before a case
              can be created. Upload a document and set its category to "Consent
              Form" to enable case creation.
            </p>
          </div>
        </div>
      </If>

      <If condition={shouldCreateCase && hasConsentForm}>
        <div className="space-y-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <FormField
              control={form.control}
              name="caseInfo.caseType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Case Type</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select case type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value={CaseTypeEnum.INITIAL_EVALUATION}>
                        <div className="flex items-center gap-2">
                          <FileTextIcon className="size-4 text-blue-600" />
                          Initial Evaluation
                        </div>
                      </SelectItem>
                      <SelectItem value={CaseTypeEnum.TRIENNIAL_EVALUATION}>
                        <div className="flex items-center gap-2">
                          <ClockIcon className="size-4 text-orange-600" />
                          Triennial Evaluation
                        </div>
                      </SelectItem>
                      <SelectItem value={CaseTypeEnum.REEVALUATION}>
                        <div className="flex items-center gap-2">
                          <FileTextIcon className="size-4 text-green-600" />
                          Reevaluation
                        </div>
                      </SelectItem>
                      <SelectItem value={CaseTypeEnum.INDEPENDENT_EVALUATION}>
                        <div className="flex items-center gap-2">
                          <FileTextIcon className="size-4 text-purple-600" />
                          Independent Evaluation
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="caseInfo.priority"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Priority</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select priority" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value={CasePriorityEnum.LOW}>
                        <div className="flex items-center gap-2">
                          <div className="size-2 rounded-full bg-green-500" />
                          Low
                        </div>
                      </SelectItem>
                      <SelectItem value={CasePriorityEnum.MEDIUM}>
                        <div className="flex items-center gap-2">
                          <div className="size-2 rounded-full bg-yellow-500" />
                          Medium
                        </div>
                      </SelectItem>
                      <SelectItem value={CasePriorityEnum.HIGH}>
                        <div className="flex items-center gap-2">
                          <div className="size-2 rounded-full bg-orange-500" />
                          High
                        </div>
                      </SelectItem>
                      <SelectItem value={CasePriorityEnum.URGENT}>
                        <div className="flex items-center gap-2">
                          <div className="size-2 rounded-full bg-violet-500" />
                          Urgent
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="caseInfo.referralDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Referral Date</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          className={cn(
                            'w-full pl-3 text-left font-normal',
                            !field.value && 'text-muted-foreground'
                          )}
                          variant="outline"
                        >
                          {field.value ? (
                            format(field.value, 'PPP')
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent align="start" className="w-auto p-0">
                      <Calendar
                        captionLayout="dropdown"
                        disabled={(date) =>
                          date > new Date() || date < new Date('1900-01-01')
                        }
                        mode="single"
                        onSelect={field.onChange}
                        selected={field.value}
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
      </If>
    </div>
  );
}