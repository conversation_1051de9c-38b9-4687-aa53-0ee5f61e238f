'use client';

import { SchoolGradeEnum } from '@lilypad/db/enums';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import { OptionalBadge } from '@lilypad/ui/components/optional-badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@lilypad/ui/components/select';
import { Toggle } from '@lilypad/ui/components/toggle';
import { SquareCheckIcon, SquareDashedIcon } from 'lucide-react';
import { useFormContext } from 'react-hook-form';
import { LanguageMultiSelectField, SchoolSelect } from '@/shared/ui/students';
import { SchoolGradeBadge } from '@/shared/ui/students/school-grade-badge';
import type { AddStudentFormData } from '../../model/schemas';

export function ContactInfoStep() {
  const form = useFormContext<AddStudentFormData>();
  const firstName = form.watch('firstName');

  return (
    <div className="space-y-6">
      <div>
        <h2 className="font-semibold text-lg">Contact & School Information</h2>
        <p className="text-muted-foreground text-sm">
          Enter the student's school and contact information. These fields are optional but recommended.
        </p>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <SchoolSelect
          fieldName="primarySchoolId"
          label="Primary School (Optional)"
          placeholder="Select primary school"
        />

        <FormField
          control={form.control}
          name="grade"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Grade <OptionalBadge />
              </FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select grade" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {Object.values(SchoolGradeEnum).map((grade) => (
                    <SelectItem key={grade} value={grade}>
                      <SchoolGradeBadge grade={grade} />
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="col-span-2">
          <LanguageMultiSelectField
            label="Languages (Optional)"
            languageIdsFieldName="languageIds"
            primaryLanguageIdFieldName="primaryLanguageId"
          />
        </div>

        <div className="col-span-2">
          <FormField
            control={form.control}
            name="specialNeedsIndicator"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Special Needs <OptionalBadge />
                </FormLabel>
                <FormControl>
                  <Toggle
                    className="w-full justify-start px-2 data-[state=on]:border-primary"
                    onPressedChange={field.onChange}
                    pressed={field.value}
                    variant="outline"
                  >
                    {field.value ? (
                      <SquareCheckIcon className="size-4 text-primary" />
                    ) : (
                      <SquareDashedIcon className="size-4 text-muted-foreground" />
                    )}
                    <span className="text-xs">
                      {firstName || 'Student'} is a special needs student
                    </span>
                  </Toggle>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>
    </div>
  );
}