import { DocumentCategoryEnum } from '@lilypad/db/enums';
import { useCallback } from 'react';

import type { AddStudentFormData } from '../schemas';
import { STEP_VALIDATION_CONFIG, StudentFormStepId } from '../step-definitions';

interface NavigationResult {
  allowed: boolean;
  reason?: string;
  blockingIssues?: string[];
}

export function useStudentFormNavigation(
  formData: Partial<AddStudentFormData>
) {
  // Helper functions
  const isBasicInfoValid = useCallback((data: Partial<AddStudentFormData>) => {
    return !!(
      data.studentIdNumber &&
      data.firstName &&
      data.lastName &&
      data.dateOfBirth &&
      data.dateOfConsent &&
      data.grade &&
      data.gender
    );
  }, []);

  const hasMinimumParents = useCallback(
    (parents: AddStudentFormData['parents']) => {
      return (
        parents &&
        parents.length > 0 &&
        parents.some((parent) => parent.isPrimaryContact)
      );
    },
    []
  );

  const hasConsentForm = useCallback(
    (documents: AddStudentFormData['documents']) => {
      return documents?.some(
        (doc) => doc.category === DocumentCategoryEnum.CONSENT_FORM
      );
    },
    []
  );

  const getBasicInfoErrors = useCallback(
    (data: Partial<AddStudentFormData>) => {
      const errors: string[] = [];
      if (!data.studentIdNumber) {
        errors.push('Student ID is required');
      }
      if (!data.firstName) {
        errors.push('First name is required');
      }
      if (!data.lastName) {
        errors.push('Last name is required');
      }
      if (!data.dateOfBirth) {
        errors.push('Date of birth is required');
      }
      if (!data.dateOfConsent) {
        errors.push('Date of consent is required');
      }
      if (!data.grade) {
        errors.push('Grade is required');
      }
      if (!data.gender) {
        errors.push('Gender is required');
      }
      return errors;
    },
    []
  );

  const canNavigateToStep = useCallback(
    (targetStep: StudentFormStepId): NavigationResult => {
      const _config = STEP_VALIDATION_CONFIG[targetStep];

      switch (targetStep) {
        case StudentFormStepId.BASIC_INFO:
          // Always accessible
          return { allowed: true };

        case StudentFormStepId.CONTACT_INFO:
        case StudentFormStepId.PARENTS:
        case StudentFormStepId.DOCUMENTS:
          // Require basic info to be valid
          if (!isBasicInfoValid(formData)) {
            return {
              allowed: false,
              reason: 'Complete basic information first',
              blockingIssues: getBasicInfoErrors(formData),
            };
          }
          return { allowed: true };

        case StudentFormStepId.CASE:
          // Require basic info and parents
          if (!isBasicInfoValid(formData)) {
            return {
              allowed: false,
              reason: 'Complete basic information first',
              blockingIssues: getBasicInfoErrors(formData),
            };
          }
          if (!hasMinimumParents(formData.parents)) {
            return {
              allowed: false,
              reason: 'Add at least one parent/guardian first',
              blockingIssues: ['At least one parent required'],
            };
          }
          return { allowed: true };

        case StudentFormStepId.REVIEW: {
          // Require all mandatory steps
          const issues: string[] = [];
          if (!isBasicInfoValid(formData)) {
            issues.push('Basic information incomplete');
          }
          if (!hasMinimumParents(formData.parents)) {
            issues.push('At least one parent required');
          }

          if (issues.length > 0) {
            return {
              allowed: false,
              reason: 'Complete required steps first',
              blockingIssues: issues,
            };
          }
          return { allowed: true };
        }

        default:
          return { allowed: false, reason: 'Unknown step' };
      }
    },
    [formData, isBasicInfoValid, hasMinimumParents, getBasicInfoErrors]
  );

  const canProceedFromStep = useCallback(
    (currentStep: StudentFormStepId): NavigationResult => {
      const _config = STEP_VALIDATION_CONFIG[currentStep];

      switch (currentStep) {
        case StudentFormStepId.BASIC_INFO:
          // Must be valid to proceed
          if (!isBasicInfoValid(formData)) {
            return {
              allowed: false,
              reason: 'Complete all required fields',
              blockingIssues: getBasicInfoErrors(formData),
            };
          }
          return { allowed: true };

        case StudentFormStepId.CONTACT_INFO:
        case StudentFormStepId.DOCUMENTS:
          // Optional steps - can always proceed
          return { allowed: true };

        case StudentFormStepId.PARENTS:
          // Must have at least one parent
          if (!hasMinimumParents(formData.parents)) {
            return {
              allowed: false,
              reason: 'Add at least one parent/guardian',
              blockingIssues: [
                'At least one parent with primary contact required',
              ],
            };
          }
          return { allowed: true };

        case StudentFormStepId.CASE:
          // Optional step - can always proceed
          return { allowed: true };

        case StudentFormStepId.REVIEW: {
          // Final step - check all requirements
          const issues: string[] = [];
          if (!isBasicInfoValid(formData)) {
            issues.push('Basic information incomplete');
          }
          if (!hasMinimumParents(formData.parents)) {
            issues.push('At least one parent required');
          }

          if (issues.length > 0) {
            return {
              allowed: false,
              reason: 'Complete required information',
              blockingIssues: issues,
            };
          }
          return { allowed: true };
        }

        default:
          return { allowed: false, reason: 'Unknown step' };
      }
    },
    [formData, isBasicInfoValid, hasMinimumParents, getBasicInfoErrors]
  );

  const isStepCompleted = useCallback(
    (stepId: StudentFormStepId): boolean => {
      switch (stepId) {
        case StudentFormStepId.BASIC_INFO:
          return isBasicInfoValid(formData);

        case StudentFormStepId.CONTACT_INFO:
          // Optional - considered completed if any field is filled
          return !!(
            formData.primarySchoolId ||
            formData.grade ||
            formData.languageIds?.length ||
            formData.specialNeedsIndicator !== undefined
          );

        case StudentFormStepId.PARENTS:
          return hasMinimumParents(formData.parents);

        case StudentFormStepId.DOCUMENTS:
          // Optional - considered completed if any documents uploaded
          return !!formData.documents?.length;

        case StudentFormStepId.CASE:
          // Optional - considered completed if case info is provided
          return !!(
            formData.caseInfo?.shouldCreateCase && formData.caseInfo?.caseType
          );

        case StudentFormStepId.REVIEW:
          // Completed when form is valid for submission
          return (
            isBasicInfoValid(formData) && hasMinimumParents(formData.parents)
          );

        default:
          return false;
      }
    },
    [formData, isBasicInfoValid, hasMinimumParents]
  );

  // Utility functions for components
  const isCaseStepEnabled = useCallback(() => {
    return hasConsentForm(formData.documents);
  }, [formData.documents, hasConsentForm]);

  return {
    canNavigateToStep,
    canProceedFromStep,
    isStepCompleted,
    isCaseStepEnabled,
    hasConsentForm: () => hasConsentForm(formData.documents),
    isBasicInfoValid: () => isBasicInfoValid(formData),
    hasMinimumParents: () => hasMinimumParents(formData.parents),
  };
}
