import { addStudentBaseSchema } from '@lilypad/api/schemas/students';
import type { z } from 'zod';

// Basic Info Step Schema - Required fields only
export const basicInfoStepSchema = addStudentBaseSchema.pick({
  studentIdNumber: true,
  firstName: true,
  middleName: true,
  lastName: true,
  preferredName: true,
  dateOfBirth: true,
  dateOfConsent: true,
  gender: true,
});

// Contact Info Step Schema - Optional fields
export const contactInfoStepSchema = addStudentBaseSchema
  .pick({
    primarySchoolId: true,
    grade: true,
    languageIds: true,
    primaryLanguageId: true,
    specialNeedsIndicator: true,
  })
  .refine(
    (data) => {
      // If languages selected, primary language must be one of them
      if (
        data.languageIds &&
        data.languageIds.length > 0 &&
        !data.primaryLanguageId
      ) {
        return false;
      }
      if (
        data.primaryLanguageId &&
        data.languageIds &&
        !data.languageIds.includes(data.primaryLanguageId)
      ) {
        return false;
      }
      return true;
    },
    {
      message:
        'Primary language must be selected and must be one of the selected languages',
      path: ['primaryLanguageId'],
    }
  );

// Parents Step Schema - At least one parent with primary contact
export const parentsStepSchema = addStudentBaseSchema.pick({
  parents: true,
});

// Documents Step Schema - Optional documents
export const documentsStepSchema = addStudentBaseSchema.pick({
  documents: true,
});

// Case Step Schema - Optional case creation
export const caseStepSchema = addStudentBaseSchema.pick({
  caseInfo: true,
});

// Notes Step Schema - Optional notes
export const notesStepSchema = addStudentBaseSchema.pick({
  notes: true,
});

// Type exports for each step
export type BasicInfoStepData = z.infer<typeof basicInfoStepSchema>;
export type ContactInfoStepData = z.infer<typeof contactInfoStepSchema>;
export type ParentsStepData = z.infer<typeof parentsStepSchema>;
export type DocumentsStepData = z.infer<typeof documentsStepSchema>;
export type CaseStepData = z.infer<typeof caseStepSchema>;
export type NotesStepData = z.infer<typeof notesStepSchema>;
