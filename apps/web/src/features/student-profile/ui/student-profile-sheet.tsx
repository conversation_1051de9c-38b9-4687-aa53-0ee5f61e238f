import type { StudentTableRow } from '@/entities/students/model/schema';
import { calculateAge } from '@/shared/lib/utils';
import { SchoolGradeBadge } from '@/shared/ui/students/school-grade-badge';
import { StudentAvatar } from '@/shared/ui/students/student-avatar';
import { StudentGenderBadge } from '@/shared/ui/students/student-gender-badge';
import { StudentStatusBadge } from '@/shared/ui/students/student-status-badge';
import { If } from '@lilypad/ui/components/if';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@lilypad/ui/components/sheet';
import {
  AnimatedTabs,
  AnimatedTabsContent,
  AnimatedTabsContentWrapper,
  AnimatedTabsList,
  AnimatedTabsTrigger,
} from '@lilypad/ui/components/tabs.animated';
import { formatDate } from '@lilypad/ui/lib/utils';
import {
  ArrowUpRightIcon,
  CakeIcon,
  ChartCandlestickIcon,
  FileTextIcon,
  FolderOpenIcon,
  NotebookPenIcon,
  NotepadTextIcon,
  School,
  UserCircleIcon,
} from 'lucide-react';
import Link from 'next/link';
import React, { useMemo } from 'react';

interface StudentProfileSheetProps {
  student?: StudentTableRow | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const StudentProfileSheet = React.memo(function StudentProfileSheetComponent({
  student,
  open,
  onOpenChange,
}: StudentProfileSheetProps) {
  const age = useMemo(() => {
    if (!student?.dateOfBirth) {
      return 0;
    }
    return calculateAge(student?.dateOfBirth);
  }, [student?.dateOfBirth]);

  if (!student) {
    return null;
  }

  return (
    <Sheet onOpenChange={onOpenChange} open={open}>
      <SheetContent
        className="m-2 h-[calc(100vh-1rem)] w-full min-w-2xl gap-0 overflow-hidden rounded-lg"
        side="right"
      >
        <SheetHeader className="flex items-center justify-between border-b p-6">
          <div className="grid grid-cols-4 gap-20">
            <div className="col-span-1">
              <StudentAvatar student={student} />
            </div>

            {/* Second Column - Student Details */}
            <div className="col-span-3 space-y-3">
              <div className="flex flex-col gap-1">
                <SheetTitle className="flex items-center space-x-4 ">
                  <span>
                    {student.firstName} {student.lastName}
                  </span>
                  <Link
                    className="flex items-center gap-2 rounded-md border border-border px-2 py-1 font-normal text-muted-foreground text-xs hover:bg-muted"
                    href={`/students/${student.id}`}
                  >
                    View Profile
                    <ArrowUpRightIcon className="-ml-1 size-3" />
                  </Link>
                </SheetTitle>

                <SheetDescription className="text-sm">
                  Prefers to be called{' '}
                  {student.preferredName || student.firstName}
                </SheetDescription>
              </div>

              <div className="flex items-center gap-2">
                <StudentGenderBadge gender={student.gender} />
                <SchoolGradeBadge grade={student.grade} />
                <StudentStatusBadge status={student.enrollmentStatus} />
              </div>

              <div className="flex items-start gap-2 pt-2 text-sm">
                <School className="size-4 text-muted-foreground" />
                <span>{student.schoolName || 'No School Assigned'}</span>
                <If condition={student.districtName}>
                  <span className="text-muted-foreground">
                    • {student.districtName}
                  </span>
                </If>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <CakeIcon className="size-4 text-muted-foreground" />
                <span>{formatDate(student.dateOfBirth)}</span>
                <span className="font-normal text-muted-foreground text-xs">
                  ({age} years old)
                </span>
              </div>
            </div>
          </div>
        </SheetHeader>

        <AnimatedTabs
          className="flex h-full flex-col"
          defaultValue="overview"
          tabs={[
            { label: 'Overview', value: 'overview' },
            { label: 'Evaluations', value: 'evaluations' },
            { label: 'Plans', value: 'plans' },
            { label: 'Documents', value: 'documents' },
            { label: 'Reports', value: 'reports' },
            { label: 'Notes', value: 'notes' },
            { label: 'Activity', value: 'activity' },
          ]}
        >
          <AnimatedTabsList className="gap-3">
            <AnimatedTabsTrigger value="overview">
              <UserCircleIcon className="size-4 text-muted-foreground" />
              Overview
            </AnimatedTabsTrigger>
            <AnimatedTabsTrigger value="evaluations">
              <ChartCandlestickIcon className="size-4 text-muted-foreground" />
              Evaluations
            </AnimatedTabsTrigger>
            <AnimatedTabsTrigger value="plans">
              <NotebookPenIcon className="size-4 text-muted-foreground" />
              Plans
            </AnimatedTabsTrigger>
            <AnimatedTabsTrigger value="documents">
              <FolderOpenIcon className="size-4 text-muted-foreground" />
              Documents
            </AnimatedTabsTrigger>
            <AnimatedTabsTrigger value="reports">
              <FileTextIcon className="size-4 text-muted-foreground" />
              Reports
            </AnimatedTabsTrigger>
            <AnimatedTabsTrigger value="notes">
              <NotepadTextIcon className="size-4 text-muted-foreground" />
              Notes
            </AnimatedTabsTrigger>
          </AnimatedTabsList>

          <AnimatedTabsContentWrapper className="flex-1 overflow-y-auto bg-muted/30 p-6">
            <AnimatedTabsContent value="overview">
              <div className="flex h-40 items-center justify-center rounded-lg border border-dashed bg-muted">
                <span className="text-muted-foreground">Overview</span>
              </div>
            </AnimatedTabsContent>
            <AnimatedTabsContent value="evaluations">
              <div className="flex h-40 items-center justify-center rounded-lg border border-dashed bg-muted">
                <span className="text-muted-foreground">Evaluations</span>
              </div>
            </AnimatedTabsContent>
            <AnimatedTabsContent value="plans">
              <div className="flex h-40 items-center justify-center rounded-lg border border-dashed bg-muted">
                <span className="text-muted-foreground">Plans</span>
              </div>
            </AnimatedTabsContent>
            <AnimatedTabsContent value="documents">
              <div className="flex h-40 items-center justify-center rounded-lg border border-dashed bg-muted">
                <span className="text-muted-foreground">Documents</span>
              </div>
            </AnimatedTabsContent>
            <AnimatedTabsContent value="reports">
              <div className="flex h-40 items-center justify-center rounded-lg border border-dashed bg-muted">
                <span className="text-muted-foreground">Reports</span>
              </div>
            </AnimatedTabsContent>
            <AnimatedTabsContent value="notes">
              <div className="flex h-40 items-center justify-center rounded-lg border border-dashed bg-muted">
                <span className="text-muted-foreground">Notes</span>
              </div>
            </AnimatedTabsContent>
            <AnimatedTabsContent value="activity">
              <div className="flex h-40 items-center justify-center rounded-lg border border-dashed bg-muted">
                <span className="text-muted-foreground">Activity</span>
              </div>
            </AnimatedTabsContent>
          </AnimatedTabsContentWrapper>
        </AnimatedTabs>
      </SheetContent>
    </Sheet>
  );
});
