import { CaseWorkflowStatusEnum } from '@lilypad/db/enums';
import type { Edge, Node } from '@xyflow/react';
import type { WorkflowLayoutConfig, WorkflowStep } from './types';

export const DEFAULT_WORKFLOW_LAYOUT: WorkflowLayoutConfig = {
  nodeWidth: 280,
  nodeHeight: 150,
  horizontalSpacing: 400,
  verticalSpacing: 300,
  stepsPerRow: 4,
  padding: 100,
};

export function createWorkflowNodes(
  steps: WorkflowStep[],
  config: WorkflowLayoutConfig = DEFAULT_WORKFLOW_LAYOUT
): Node[] {
  return steps.map((step, index): Node => {
    const row = Math.floor(index / config.stepsPerRow);
    const col = index % config.stepsPerRow;

    return {
      id: step.id,
      type: 'workflowStep',
      position: {
        x: col * config.horizontalSpacing + config.padding,
        y: row * config.verticalSpacing + config.padding,
      },
      data: { step },
      width: config.nodeWidth,
      height: config.nodeHeight,
      style: {
        width: config.nodeWidth,
        height: config.nodeHeight,
      },
    };
  });
}

export function createWorkflowEdges(steps: WorkflowStep[]): Edge[] {
  const edges: Edge[] = [];

  for (let i = 0; i < steps.length - 1; i++) {
    const currentStep = steps[i];
    const nextStep = steps[i + 1];

    edges.push({
      id: `edge-${currentStep.id}-${nextStep.id}`,
      source: currentStep.id,
      target: nextStep.id,
      type: 'smoothstep',
      animated: currentStep.status === CaseWorkflowStatusEnum.COMPLETED,
      style: {
        strokeWidth: 2,
        stroke: getEdgeColor(currentStep.status),
      },
    });
  }

  return edges;
}

function getEdgeColor(status: CaseWorkflowStatusEnum): string {
  switch (status) {
    case CaseWorkflowStatusEnum.COMPLETED:
      return '#22c55e'; // green-500
    case CaseWorkflowStatusEnum.IN_PROGRESS:
      return '#3b82f6'; // blue-500
    case CaseWorkflowStatusEnum.SKIPPED:
      return '#9ca3af'; // gray-400
    default:
      return '#d1d5db'; // gray-300
  }
}

export function calculateWorkflowDuration(steps: WorkflowStep[]): number {
  return steps.reduce((total, step) => {
    return total + (step.estimatedDays || 0);
  }, 0);
}

export function getWorkflowProgress(steps: WorkflowStep[]): number {
  if (steps.length === 0) {
    return 0;
  }

  const completedSteps = steps.filter(
    (step) => step.status === CaseWorkflowStatusEnum.COMPLETED
  ).length;

  return Math.round((completedSteps / steps.length) * 100);
}

export function getCurrentStep(steps: WorkflowStep[]): WorkflowStep | null {
  return (
    steps.find((step) => step.status === CaseWorkflowStatusEnum.IN_PROGRESS) ||
    null
  );
}

export function getNextStep(steps: WorkflowStep[]): WorkflowStep | null {
  return (
    steps.find((step) => step.status === CaseWorkflowStatusEnum.PENDING) || null
  );
}
