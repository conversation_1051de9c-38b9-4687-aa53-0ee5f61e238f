import type { CaseDetailsData } from '@/entities/cases';

export type WorkflowStep = NonNullable<CaseDetailsData['workflow']>['steps'][0];

export interface WorkflowDiagramProps {
  workflow: CaseDetailsData['workflow'];
}

export interface WorkflowStepNodeProps {
  step: WorkflowStep;
}

export interface WorkflowNodeData {
  step: WorkflowStep;
}

export interface WorkflowLayoutConfig {
  nodeWidth: number;
  nodeHeight: number;
  horizontalSpacing: number;
  verticalSpacing: number;
  stepsPerRow: number;
  padding: number;
}

export interface LegendItem {
  icon: React.ReactNode;
  label: string;
  description: string;
}
