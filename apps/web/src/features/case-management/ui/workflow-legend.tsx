import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@lilypad/ui/components/accordion';
import { Card, CardContent } from '@lilypad/ui/components/card';
import { CheckCircle2Icon, CircleIcon, ClockIcon, InfoIcon, XCircleIcon } from 'lucide-react';
import type { LegendItem } from '../lib/types';

const LEGEND_ITEMS: LegendItem[] = [
  {
    icon: <CheckCircle2Icon className="size-4 text-green-600" />,
    label: 'Completed',
    description: 'Step has been finished',
  },
  {
    icon: <ClockIcon className="size-4 text-blue-600" />,
    label: 'In Progress',
    description: 'Currently being worked on',
  },
  {
    icon: <CircleIcon className="size-4 text-gray-400" />,
    label: 'Pending',
    description: 'Not yet started',
  },
  {
    icon: <XCircleIcon className="size-4 text-gray-400" />,
    label: 'Skipped',
    description: 'Step was skipped',
  },
];

function LegendItemComponent({ icon, label, description }: LegendItem) {
  return (
    <div className="flex items-center gap-2">
      {icon}
      <div className="flex flex-col">
        <span className="font-medium text-xs">{label}</span>
        <span className="text-muted-foreground text-xs">{description}</span>
      </div>
    </div>
  );
}

export function WorkflowLegend() {
  return (
    <div className="absolute bottom-4 left-4 z-10 w-52">
      <Card className="p-0">
        <CardContent className="p-0">
          <Accordion className="w-full p-0" collapsible type="single">
            <AccordionItem className="border-0" value="legend">
              <AccordionTrigger className="px-3 py-2 hover:no-underline [&>svg]:hidden">
                <div className="flex items-center gap-2">
                  <InfoIcon className="size-4" />
                  <span className="text-xs">Legend</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-3 pb-3">
                <div className="space-y-2">
                  {LEGEND_ITEMS.map((item, index) => (
                    <LegendItemComponent
                      description={item.description}
                      icon={item.icon}
                      key={`${item.label}-${index}`}
                      label={item.label}
                    />
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </CardContent>
      </Card>
    </div>
  );
}
