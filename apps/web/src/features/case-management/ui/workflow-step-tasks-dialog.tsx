'use client';

import { useTR<PERSON> } from '@lilypad/api/client';
import type { TaskWithRelations } from '@lilypad/db/repository/types/tasks';
import { Badge } from '@lilypad/ui/components/badge';
import { Button } from '@lilypad/ui/components/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@lilypad/ui/components/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@lilypad/ui/components/drawer';
import { If } from '@lilypad/ui/components/if';
import { ScrollArea } from '@lilypad/ui/components/scroll-area';
import { useMediaQuery } from '@lilypad/ui/hooks/use-media-query';
import { MediaQueries } from '@lilypad/ui/lib/media-queries';
import { cn } from '@lilypad/ui/lib/utils';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import {
  AlertCircle,
  Calendar,
  CheckCircle2,
  Clock,
  FileText,
  ListTodoIcon,
  User,
  Users,
} from 'lucide-react';
import type { WorkflowStep } from '../lib/types';
import { CaseWorkflowStepStatusBadge } from '@/shared/ui/cases';

interface WorkflowStepTasksDialogProps {
  step: WorkflowStep | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface TaskItemProps {
  task: TaskWithRelations;
}

function TaskItem({ task }: TaskItemProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircle2 className="size-4 text-green-600" />;
      case 'IN_PROGRESS':
        return <Clock className="size-4 text-blue-600" />;
      case 'REJECTED':
        return <AlertCircle className="size-4 text-red-600" />;
      default:
        return <FileText className="size-4 text-muted-foreground" />;
    }
  };

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'default' as const;
      case 'IN_PROGRESS':
        return 'secondary' as const;
      case 'REJECTED':
        return 'destructive' as const;
      default:
        return 'outline' as const;
    }
  };

  const getPriorityVariant = (priority: string) => {
    switch (priority) {
      case 'HIGH':
      case 'URGENT':
        return 'destructive' as const;
      case 'MEDIUM':
        return 'secondary' as const;
      default:
        return 'outline' as const;
    }
  };

  return (
    <div className="space-y-3 rounded-lg border p-4">
      <div className="flex items-start justify-between">
        <div className="flex items-center gap-2">
          {getStatusIcon(task.status)}
          <h4 className="font-medium">{task.taskType}</h4>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={getPriorityVariant(task.priority)}>
            {task.priority}
          </Badge>
          <Badge variant={getStatusVariant(task.status)}>{task.status}</Badge>
        </div>
      </div>

      <If condition={task.notes}>
        <p className="text-muted-foreground text-sm">{task.notes}</p>
      </If>

      <div className="grid grid-cols-1 gap-3 text-sm md:grid-cols-2">
        <div className="flex items-center gap-2">
          <User className="size-4 text-muted-foreground" />
          <span className="font-medium">Assigned to:</span>
          <span>{task.assignedTo.fullName}</span>
        </div>

        <div className="flex items-center gap-2">
          <Users className="size-4 text-muted-foreground" />
          <span className="font-medium">Assigned by:</span>
          <span>{task.assignedBy.fullName}</span>
        </div>

        <If condition={task.dueDate}>
          <div className="flex items-center gap-2">
            <Calendar className="size-4 text-muted-foreground" />
            <span className="font-medium">Due:</span>
            <span>
              {task.dueDate
                ? format(new Date(task.dueDate), 'MMM d, yyyy')
                : 'N/A'}
            </span>
          </div>
        </If>

        <div className="flex items-center gap-2">
          <Clock className="size-4 text-muted-foreground" />
          <span className="font-medium">Created:</span>
          <span>{format(new Date(task.createdAt), 'MMM d, yyyy')}</span>
        </div>

        <If condition={task.completedAt}>
          <div className="flex items-center gap-2">
            <CheckCircle2 className="size-4 text-muted-foreground" />
            <span className="font-medium">Completed:</span>
            <span>
              {task.completedAt
                ? format(new Date(task.completedAt), 'MMM d, yyyy')
                : 'N/A'}
            </span>
          </div>
        </If>

        <If condition={task.rejectedAt}>
          <div className="flex items-center gap-2">
            <AlertCircle className="size-4 text-muted-foreground" />
            <span className="font-medium">Rejected:</span>
            <span>
              {task.rejectedAt
                ? format(new Date(task.rejectedAt), 'MMM d, yyyy')
                : 'N/A'}
            </span>
          </div>
        </If>
      </div>

      <If condition={task.reason}>
        <div className="border-t pt-2">
          <p className="text-sm">
            <span className="font-medium">Reason:</span> {task.reason}
          </p>
        </div>
      </If>
    </div>
  );
}

function TasksList({ tasks }: { tasks: TaskWithRelations[] }) {
  if (tasks.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <ListTodoIcon className="mb-4 size-12 text-muted-foreground" />
        <h3 className="mb-2 font-medium text-lg">No tasks found</h3>
        <p className="max-w-sm text-muted-foreground text-sm">
          There are no tasks currently associated with this step.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {tasks.map((task) => (
        <TaskItem key={task.id} task={task} />
      ))}
    </div>
  );
}

function DialogContentComponent({ step }: { step: WorkflowStep }) {
  const trpc = useTRPC();
  const { data: tasks, isError } = useQuery(
    trpc.cases.getTasksByWorkflowStepId.queryOptions(step.id)
  );

  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <AlertCircle className="mb-4 size-12 text-red-500" />
        <h3 className="mb-2 font-medium text-lg">Error loading tasks</h3>
        <p className="text-muted-foreground text-sm">
          Unable to load tasks for this workflow step. Please try again.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4 ">
      <h4 className="mb-4 flex items-center gap-2 font-semibold text-sm">
        <span>Tasks</span>
        <Badge size="sm" variant="secondary">
          {tasks?.length}
        </Badge>
      </h4>
      <TasksList tasks={tasks ?? []} />
    </div>
  );
}

export function WorkflowStepTasksDialog({
  step,
  open,
  onOpenChange,
}: WorkflowStepTasksDialogProps) {
  const mdUp = useMediaQuery(MediaQueries.MdUp);
  const renderContent = () => {
    if (!step) {
      return (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <AlertCircle className="mb-4 size-12 text-muted-foreground" />
          <h3 className="mb-2 font-medium text-lg">No step selected</h3>
          <p className="text-muted-foreground text-sm">
            Please select a workflow step to view its tasks.
          </p>
        </div>
      );
    }

    return <DialogContentComponent step={step} />;
  };

  const renderButtons = (
    <div
      className={cn(
        'flex flex-shrink-0 justify-end gap-2 bg-secondary p-4',
        mdUp && 'rounded-b-md'
      )}
    >
      <Button
        onClick={() => onOpenChange(false)}
        size="sm"
        type="button"
        variant="outline"
      >
        Close
      </Button>
    </div>
  );

  return (
    <>
      {mdUp ? (
        <Dialog onOpenChange={onOpenChange} open={open}>
          <DialogContent className="flex max-h-[85vh] max-w-4xl flex-col gap-0 p-0">
            <DialogHeader className="flex-shrink-0 border-b px-6 py-5">
              <DialogTitle className="flex items-center gap-2 font-semibold text-lg">
                {step?.name}
                <CaseWorkflowStepStatusBadge
                  size="sm"
                  status={step?.status!}
                />
              </DialogTitle>
              <DialogDescription className="text-muted-foreground text-sm">
                {step?.description}
              </DialogDescription>
            </DialogHeader>
            <ScrollArea className="flex-1 px-6 py-4">
              {renderContent()}
            </ScrollArea>
            {renderButtons}
          </DialogContent>
        </Dialog>
      ) : (
        <Drawer onOpenChange={onOpenChange} open={open}>
          <DrawerContent className="flex max-h-[90vh] flex-col">
            <DrawerHeader className="flex-shrink-0 border-b text-left">
              <DrawerTitle className="font-semibold text-lg">
                {step?.name}
              </DrawerTitle>
              <DrawerDescription className="text-muted-foreground text-sm">
                {step?.description}
              </DrawerDescription>
            </DrawerHeader>
            <ScrollArea className="flex-1 px-4 py-4">
              {renderContent()}
            </ScrollArea>
            {renderButtons}
          </DrawerContent>
        </Drawer>
      )}
    </>
  );
}
