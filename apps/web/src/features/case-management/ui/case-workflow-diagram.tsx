'use client';

import '@xyflow/react/dist/style.css';

import {
  Background,
  BackgroundVariant,
  Controls,
  type Node,
  ReactFlow,
  ReactFlowProvider,
  useEdgesState,
  useNodesState,
} from '@xyflow/react';
import { useCallback, useMemo, useState } from 'react';
import type { CaseDetailsData } from '@/entities/cases';
import type { WorkflowDiagramProps, WorkflowStep } from '../lib/types';
import {
  createWorkflowEdges,
  createWorkflowNodes,
} from '../lib/workflow-utils';
import { WorkflowLegend } from './workflow-legend';
import { WorkflowStepNode } from './workflow-step-node';
import { WorkflowStepTasksDialog } from './workflow-step-tasks-dialog';

const WORKFLOW_NODE_TYPES = {
  workflowStep: WorkflowStepNode,
};

interface CaseWorkflowContentProps {
  workflow: CaseDetailsData['workflow'];
  onNodeClick: (step: WorkflowStep) => void;
}

function CaseWorkflowContent({
  workflow,
  onNodeClick,
}: CaseWorkflowContentProps) {
  const initialNodes = useMemo(() => {
    if (!workflow?.steps) {
      return [];
    }
    return createWorkflowNodes(workflow.steps);
  }, [workflow?.steps]);

  const initialEdges = useMemo(() => {
    if (!workflow?.steps) {
      return [];
    }
    return createWorkflowEdges(workflow.steps);
  }, [workflow?.steps]);

  const [nodes, , onNodesChange] = useNodesState(initialNodes);
  const [edges, , onEdgesChange] = useEdgesState(initialEdges);

  const handleNodeClick = useCallback(
    (_event: React.MouseEvent, node: Node) => {
      const { step } = node.data as { step: WorkflowStep };
      onNodeClick(step);
    },
    [onNodeClick]
  );

  return (
    <div className="h-[calc(100vh-3rem)] w-full pt-10">
      <ReactFlow
        attributionPosition="bottom-left"
        edges={edges}
        fitView
        nodes={nodes}
        nodesDraggable={false}
        nodeTypes={WORKFLOW_NODE_TYPES}
        onEdgesChange={onEdgesChange}
        onNodeClick={handleNodeClick}
        onNodesChange={onNodesChange}
      >
        <Background gap={16} size={1} variant={BackgroundVariant.Dots} />
        <Controls position="bottom-right" showInteractive={false} />
        <WorkflowLegend />
      </ReactFlow>
    </div>
  );
}

export function CaseWorkflowDiagram({ workflow }: WorkflowDiagramProps) {
  const [selectedStep, setSelectedStep] = useState<WorkflowStep | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleNodeClick = useCallback((step: WorkflowStep) => {
    setSelectedStep(step);
    setIsDialogOpen(true);
  }, []);

  const handleDialogClose = useCallback((open: boolean) => {
    setIsDialogOpen(open);
    if (!open) {
      setSelectedStep(null);
    }
  }, []);

  return (
    <ReactFlowProvider>
      <CaseWorkflowContent onNodeClick={handleNodeClick} workflow={workflow} />
      <WorkflowStepTasksDialog
        onOpenChange={handleDialogClose}
        open={isDialogOpen}
        step={selectedStep}
      />
    </ReactFlowProvider>
  );
}
