import { useTRPC } from '@lilypad/api/client';
import type { GetNotificationsInput } from '@lilypad/api/schemas/notifications';
import { useInfiniteQuery } from '@tanstack/react-query';

export function useInfiniteNotificationsQuery(input: GetNotificationsInput) {
  const trpc = useTRPC();

  const queryResult = useInfiniteQuery(
    trpc.notifications.getNotifications.infiniteQueryOptions(input, {
      getNextPageParam: (lastPage) => lastPage.nextCursor,
      initialCursor: input.cursor,
      staleTime: 1000 * 30, // 30 seconds
    })
  );

  return queryResult;
}
