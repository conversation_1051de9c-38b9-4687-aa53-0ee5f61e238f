'use client';

import { Button } from '@lilypad/ui/components/button';
import {
  Card,
  CardContent,
} from '@lilypad/ui/components/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
} from '@lilypad/ui/components/form';
import { Switch } from '@lilypad/ui/components/switch';
import { useDistrictPreferencesForm } from '../model/use-district-preferences-form';

export function DistrictPreferencesCard() {
  const { methods, canSubmit, onSubmit } = useDistrictPreferencesForm();

  return (
    <div className="max-w-2xl rounded-xl border border-border bg-muted/80 dark:bg-muted/20">
      <Form {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmit)}>
          <Card className="gap-2 border-border border-x-0 border-t-0 border-b-xl shadow-none">
            <CardContent className="space-y-4">
              <FormField
                control={methods.control}
                name="chromebookSetup"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel>Chromebook for evaluations set-up</FormLabel>
                      <FormDescription className="text-xs">
                        Receive Chromebook for evaluations set-up and support.
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={methods.formState.isSubmitting}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={methods.control}
                name="quietSpaceForEvaluation"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel>
                        Quiet space for evaluation to take place at schools
                      </FormLabel>
                      <FormDescription className="text-xs">
                        Provide a quiet space for evaluations to take place at
                        schools.
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={methods.formState.isSubmitting}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={methods.control}
                name="internetStability"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel>Internet stability at school buildings</FormLabel>
                      <FormDescription className="text-xs">
                        Ensure reliable internet connectivity at school buildings for
                        evaluations.
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={methods.formState.isSubmitting}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>
          <div className="flex items-center justify-end space-x-2 p-3">
            <Button
              type="submit"
              variant="default"
              size="sm"
              disabled={!canSubmit}
              loading={methods.formState.isSubmitting}
            >
              Save
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
} 