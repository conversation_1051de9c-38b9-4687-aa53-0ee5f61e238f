'use client';

import type { UpdateDistrictAvailabilitiesSchema } from '@lilypad/api/schemas/districts';
import { Button } from '@lilypad/ui/components/button';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@lilypad/ui/components/form';
import { If } from '@lilypad/ui/components/if';
import { Switch } from '@lilypad/ui/components/switch';
import { TimePicker } from '@lilypad/ui/components/time-picker';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@lilypad/ui/components/tooltip';
import { PlusIcon, Trash2Icon } from 'lucide-react';
import { memo, useCallback } from 'react';
import { useFormContext } from 'react-hook-form';
import { DAYS_OF_WEEK } from '@/features/district-onboarding/model/constants';
import { CopyTimesPopover } from './copy-times-popover';

type DayOfWeek = keyof UpdateDistrictAvailabilitiesSchema['availabilitySchedule'];
type DayConfig = {
  key: DayOfWeek;
  label: string;
}

interface DistrictAvailabilityDayRowProps {
  dayConfig: DayConfig;
  onFormChange?: () => void;
}

export const DistrictAvailabilityDayRow = memo(function DistrictAvailabilityDayRowComponent({
  dayConfig,
  onFormChange,
}: DistrictAvailabilityDayRowProps) {
  const form = useFormContext<UpdateDistrictAvailabilitiesSchema>();
  const { key: dayKey, label } = dayConfig;

  const isEnabled = form.watch(
    `availabilitySchedule.${dayKey}.enabled` as const
  );

  const timeSlots =
    form.watch(
      `availabilitySchedule.${dayKey}.slots` as const
    ) || [];

  const addTimeSlot = useCallback(() => {
    const currentSlots =
      form.getValues(
        `availabilitySchedule.${dayKey}.slots` as const
      ) || [];

    // Calculate next time slot (simple logic: add 1 hour after last slot or default)
    const newSlot = currentSlots.length > 0
      ? { startTime: '14:00', endTime: '17:00' }
      : { startTime: '09:00', endTime: '17:00' };

    form.setValue(
      `availabilitySchedule.${dayKey}.slots` as const,
      [...currentSlots, newSlot]
    );

    onFormChange?.();
  }, [form, dayKey, onFormChange]);

  const removeTimeSlot = useCallback(
    (index: number) => {
      const currentSlots =
        form.getValues(
          `availabilitySchedule.${dayKey}.slots` as const
        ) || [];
      form.setValue(
        `availabilitySchedule.${dayKey}.slots` as const,
        currentSlots.filter((_, i) => i !== index)
      );
      onFormChange?.();
    },
    [form, dayKey, onFormChange]
  );

  const handleToggleDay = useCallback(
    (enabled: boolean) => {
      form.setValue(
        `availabilitySchedule.${dayKey}.enabled` as const,
        enabled
      );

      // Initialize first slot when enabling a day
      if (enabled && timeSlots.length === 0) {
        form.setValue(
          `availabilitySchedule.${dayKey}.slots` as const,
          [{ startTime: '09:00', endTime: '17:00' }]
        );
      }

      onFormChange?.();
    },
    [form, dayKey, onFormChange, timeSlots.length]
  );

  return (
    <div className="flex flex-col flex-wrap gap-2 sm:flex-row sm:items-start sm:gap-4">
      <div className="flex-shrink-0 sm:w-28">
        <DayToggleSection
          dayKey={dayKey}
          isEnabled={isEnabled}
          label={label}
          onToggle={handleToggleDay}
        />
      </div>

      <div className="flex-1">
        <If condition={isEnabled && timeSlots.length > 0}>
          <div className="flex flex-col gap-2">
            {timeSlots.map((_, index) => (
              <TimeSlotRow
                key={index}
                canRemove={index > 0}
                dayKey={dayKey}
                onRemove={removeTimeSlot}
                slotIndex={index}
              />
            ))}
          </div>
        </If>

        <If condition={!isEnabled}>
          <div className="flex items-center justify-center rounded-md border border-dashed bg-muted p-2 text-center text-muted-foreground text-sm">
            Unavailable
          </div>
        </If>
      </div>

      {/* Actions Section - Full width on mobile, auto width on desktop */}
      <div className="flex justify-start sm:flex-shrink-0">
        <If condition={isEnabled}>
          <DayActions dayKey={dayKey} onAddTimeSlot={addTimeSlot} />
        </If>
      </div>
    </div>
  );
});

interface DayToggleSectionProps {
  dayKey: DayOfWeek;
  label: string;
  isEnabled: boolean;
  onToggle: (enabled: boolean) => void;
}

const DayToggleSection = memo(function DayToggleSectionComponent({
  dayKey,
  label,
  onToggle,
}: DayToggleSectionProps) {
  const form = useFormContext<UpdateDistrictAvailabilitiesSchema>();

  return (
    <FormField
      control={form.control}
      name={`availabilitySchedule.${dayKey}.enabled` as const}
      render={({ field }) => (
        <FormItem className="flex items-center gap-2">
          <FormControl>
            <Switch
              checked={field.value}
              onCheckedChange={(checked) => {
                field.onChange(checked);
                onToggle(checked);
              }}
            />
          </FormControl>
          <FormLabel className="cursor-pointer font-medium text-sm leading-none">
            {label}
          </FormLabel>
        </FormItem>
      )}
    />
  );
});

interface TimeSlotRowProps {
  dayKey: DayOfWeek;
  slotIndex: number;
  onFormChange?: () => void;
  canRemove: boolean;
  onRemove: (index: number) => void;
}

const TimeSlotRow = memo(function TimeSlotRowComponent({
  dayKey,
  slotIndex,
  onFormChange,
  canRemove,
  onRemove,
}: TimeSlotRowProps) {
  const form = useFormContext<UpdateDistrictAvailabilitiesSchema>();

  return (
    <div className="flex items-center gap-2">
      <div className="flex items-center gap-2">
        <FormField
          control={form.control}
          name={
            `availabilitySchedule.${dayKey}.slots.${slotIndex}.startTime` as const
          }
          render={({ field }) => (
            <FormControl>
              <TimePicker
                className="min-w-[120px] max-w-full"
                onChange={(value) => {
                  field.onChange(value);
                  onFormChange?.();
                }}
                placeholder="Start"
                value={field.value}
              />
            </FormControl>
          )}
        />
        <span className="text-muted-foreground text-sm">-</span>
        <FormField
          control={form.control}
          name={
            `availabilitySchedule.${dayKey}.slots.${slotIndex}.endTime` as const
          }
          render={({ field }) => (
            <FormControl>
              <TimePicker
                className="min-w-[120px] max-w-full"
                onChange={(value) => {
                  field.onChange(value);
                  onFormChange?.();
                }}
                placeholder="End"
                value={field.value}
              />
            </FormControl>
          )}
        />
      </div>

      {/* Remove Button */}
      <If condition={canRemove}>
        <Button
          className="size-8 flex-shrink-0"
          onClick={() => onRemove(slotIndex)}
          size="icon"
          type="button"
          variant="ghost"
        >
          <Trash2Icon className="size-4" />
        </Button>
      </If>
    </div>
  );
});

interface DayActionsProps {
  dayKey: DayOfWeek;
  onAddTimeSlot: () => void;
}

const DayActions = memo(function DayActionsComponent({
  dayKey,
  onAddTimeSlot,
}: DayActionsProps) {
  return (
    <div className="flex flex-shrink-0 items-center gap-1">
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            className="size-8"
            onClick={onAddTimeSlot}
            size="icon"
            type="button"
            variant="ghost"
          >
            <PlusIcon className="size-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Add time slot</p>
        </TooltipContent>
      </Tooltip>

      <CopyTimesPopover enabledDays={DAYS_OF_WEEK} sourceDayKey={dayKey} />
    </div>
  );
});