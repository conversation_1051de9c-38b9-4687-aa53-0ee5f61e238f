'use client';
import { FileUploadAction, MAX_IMAGE_SIZE } from '@lilypad/shared/file-upload';
import { Avatar, AvatarFallback } from '@lilypad/ui/components/avatar';
import { Button } from '@lilypad/ui/components/button';
import {
  Card,
  CardContent,
} from '@lilypad/ui/components/card';
import {
  Form,
} from '@lilypad/ui/components/form';
import { If } from '@lilypad/ui/components/if';
import { ImageDropzone } from '@lilypad/ui/components/image-dropzone';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from '@lilypad/ui/components/tooltip';
import { Trash2Icon, UploadIcon } from 'lucide-react';
import type * as React from 'react';
import { useMemo } from 'react';
import { useDistrictLogoForm } from '../model/use-district-logo-form';

export function DistrictLogoCard(): React.JSX.Element {
  const {
    onDrop,
    onRemoveImage,
    onSubmit,
    methods,
    district,
    isSaving,
  } = useDistrictLogoForm();

  const image = methods.watch('image');
  const action = methods.watch('action');
  const defaultImageUrl = district?.logo;

  const imageUrl = useMemo(() => {
    if (image) {
      return URL.createObjectURL(image as File);
    }
    if (action === FileUploadAction.Delete) {
      return;
    }
    if (defaultImageUrl) {
      return defaultImageUrl;
    }
  }, [image, action, defaultImageUrl]);

  return (
    <Form {...methods}>
      <Card className="max-w-2xl gap-2 border-border shadow-none">
        <CardContent>
          <form onSubmit={methods.handleSubmit(onSubmit)} className="flex items-center space-x-4">
            <div className="group/district-logo relative ">
              <ImageDropzone
                accept={{ 'image/*': [] }}
                multiple={false}
                borderRadius="md"
                onDrop={onDrop}
                src={imageUrl}
                className="size-20 rounded-xl p-0.5"
                disabled={isSaving}
                maxSize={MAX_IMAGE_SIZE}
              >
                <Avatar className="size-[72px] rounded-md">
                  <AvatarFallback className="size-[72px] rounded-md text-2xl">
                    <UploadIcon className="size-5 shrink-0 text-muted-foreground" />
                  </AvatarFallback>
                </Avatar>
              </ImageDropzone>
              <If condition={imageUrl}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      type="button"
                      variant="cancel"
                      size="icon"
                      className="-bottom-1 -right-1 absolute z-10 size-6 rounded-full bg-background opacity-0 group-hover/district-logo:opacity-100"
                      disabled={isSaving}
                      onClick={onRemoveImage}
                    >
                      <Trash2Icon className="size-3 shrink-0" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="right">Remove logo</TooltipContent>
                </Tooltip>
              </If>
            </div>
            <div className="flex flex-col space-y-1">
              <span className="text-sm">Click or drag to upload</span>
              <span className="text-muted-foreground text-xs">
                *.png, *.jpeg files up to 5 MB
              </span>
            </div>
          </form>
        </CardContent>
      </Card>
    </Form>
  );
}
