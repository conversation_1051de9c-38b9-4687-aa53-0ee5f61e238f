'use client';

import { DistrictTypeMap } from '@lilypad/db/enums';
import { generateSlug } from '@lilypad/shared';
import { Button } from '@lilypad/ui/components/button';
import {
  Card,
  CardContent,
} from '@lilypad/ui/components/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import { Input } from '@lilypad/ui/components/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@lilypad/ui/components/select';
import { useDistrictDetailsForm } from '../model/use-district-details-form';

export function DistrictDetailsCard() {
  const { methods, canSubmit, onSubmit } = useDistrictDetailsForm();

  const handleNameChange = (value: string) => {
    methods.setValue('name', value);
    methods.setValue('slug', generateSlug(value));
  };

  return (
    <div className="max-w-2xl rounded-xl border border-border bg-muted/80 dark:bg-muted/20">
      <Form {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmit)}>
          <Card className="gap-2 border-border border-x-0 border-t-0 border-b-xl shadow-none">
            <CardContent>
              <div className="space-y-6">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField
                    control={methods.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>District Name</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter district name"
                            disabled={methods.formState.isSubmitting}
                            {...field}
                            onChange={(e) => handleNameChange(e.target.value)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={methods.control}
                    name="slug"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Slug</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="district-slug"
                            disabled={methods.formState.isSubmitting}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={methods.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>District Type</FormLabel>
                        <Select
                          defaultValue={field.value}
                          onValueChange={field.onChange}
                          disabled={methods.formState.isSubmitting}
                        >
                          <FormControl>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select district type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {Object.entries(DistrictTypeMap).map(([value, label]) => (
                              <SelectItem key={value} value={value}>
                                {label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={methods.control}
                    name="website"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Website</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="https://district.edu"
                            type="url"
                            disabled={methods.formState.isSubmitting}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={methods.control}
                    name="ncesId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>NCES ID</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter NCES ID"
                            disabled={methods.formState.isSubmitting}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={methods.control}
                    name="stateId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>State ID</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter state ID"
                            disabled={methods.formState.isSubmitting}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={methods.control}
                    name="county"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>County</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter county"
                            disabled={methods.formState.isSubmitting}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={methods.control}
                    name="invoiceEmail"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Invoice Email</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="Enter invoice email"
                            type="email"
                            disabled={methods.formState.isSubmitting}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={methods.control}
                    name="numSchools"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Number of Schools</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            onChange={(e) => {
                              const value = e.target.value;
                              field.onChange(
                                value === '' ? 0 : Number.parseInt(value, 10)
                              );
                            }}
                            placeholder="Enter number of schools"
                            type="number"
                            min="0"
                            disabled={methods.formState.isSubmitting}
                            value={field.value === 0 ? '' : field.value}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={methods.control}
                    name="numStudents"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Number of Students</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            onChange={(e) => {
                              const value = e.target.value;
                              field.onChange(
                                value === '' ? 0 : Number.parseInt(value, 10)
                              );
                            }}
                            placeholder="Enter number of students"
                            type="number"
                            min="0"
                            disabled={methods.formState.isSubmitting}
                            value={field.value === 0 ? '' : field.value}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
          <div className="flex items-center justify-end space-x-2 p-3">
            <Button
              type="submit"
              variant="default"
              size="sm"
              disabled={!canSubmit}
              loading={methods.formState.isSubmitting}
            >
              Save
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
