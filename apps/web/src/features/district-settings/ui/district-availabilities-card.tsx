'use client';

import { Button } from '@lilypad/ui/components/button';
import {
  Card,
  CardContent,
} from '@lilypad/ui/components/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@lilypad/ui/components/select';
import { memo } from 'react';
import { useDistrictAvailabilitiesForm } from '../model/use-district-availabilities-form';
import { DistrictAvailabilityDayRow } from './district-availability-day-row';

const DAYS_OF_WEEK = [
  { key: 'monday', label: 'Monday' },
  { key: 'tuesday', label: 'Tuesday' },
  { key: 'wednesday', label: 'Wednesday' },
  { key: 'thursday', label: 'Thursday' },
  { key: 'friday', label: 'Friday' },
  { key: 'saturday', label: 'Saturday' },
  { key: 'sunday', label: 'Sunday' },
] as const;

export function DistrictAvailabilitiesCard() {
  const { methods, canSubmit, onSubmit, timezoneOptions } = useDistrictAvailabilitiesForm();

  return (
    <div className="max-w-2xl rounded-xl border border-border bg-muted/80 dark:bg-muted/20">
      <Form {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmit)}>
          <Card className="gap-2 border-border border-x-0 border-t-0 border-b-xl shadow-none">
            <CardContent className="space-y-6">
              <TimezoneSelector
                timezoneOptions={timezoneOptions}
              />

              {DAYS_OF_WEEK.map((dayConfig) => (
                <DistrictAvailabilityDayRow
                  dayConfig={dayConfig}
                  key={dayConfig.key}
                />
              ))}

            </CardContent>
          </Card>
          <div className="flex items-center justify-end space-x-2 p-3">
            <Button
              type="submit"
              variant="default"
              size="sm"
              disabled={!canSubmit}
              loading={methods.formState.isSubmitting}
            >
              Save
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}

interface TimezoneSelectorProps {
  timezoneOptions: Array<{
    value: string;
    label: string;
    prefix: string;
  }>;
}

const TimezoneSelector = memo(function TimezoneSelectorComponent({
  timezoneOptions,
}: TimezoneSelectorProps) {
  return (
    <FormField
      name="timezone"
      render={({ field }) => (
        <FormItem className="flex flex-col xl:flex-row">
          <div className="flex w-full flex-col gap-1">
            <FormLabel>Timezone</FormLabel>
            <FormDescription className='text-xs'>
              Select the timezone of evaluation hours.
            </FormDescription>
          </div>
          <Select onValueChange={field.onChange} value={field.value}>
            <FormControl>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select timezone" />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {timezoneOptions.map((timezone) => (
                <SelectItem key={timezone.value} value={timezone.value}>
                  <div className="flex items-center gap-1">
                    <span className="rounded-sm bg-muted px-1 py-0.5 text-muted-foreground text-xs">
                      {timezone.prefix}
                    </span>
                    <span className="text-sm">{timezone.label}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}); 