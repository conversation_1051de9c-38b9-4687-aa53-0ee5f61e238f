import {
  Card,
  CardContent,
  type CardProps
} from '@lilypad/ui/components/card';
import { Skeleton } from '@lilypad/ui/components/skeleton';

export function DistrictPreferencesSkeletonCard(
  props: CardProps
): React.JSX.Element {
  return (
    <div className="max-w-2xl rounded-xl border border-border bg-muted/80 dark:bg-muted/20">
      <Card className="gap-2 border-border border-x-0 border-t-0 border-b-xl shadow-none" {...props}>
        <CardContent>
          <div className="space-y-6">
            {Array.from({ length: 3 }).map((_, i) => (
              <div
                key={i}
                className="flex flex-row items-center justify-between rounded-lg border p-4"
              >
                <div className="space-y-0.5">
                  <Skeleton className="h-4 w-48" />
                  <Skeleton className="h-3 w-64" />
                </div>
                <Skeleton className="h-6 w-11 rounded-full" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
      <div className="flex items-center justify-end space-x-2 p-3">
        <Skeleton className="h-8 w-24" />
      </div>
    </div>
  );
} 