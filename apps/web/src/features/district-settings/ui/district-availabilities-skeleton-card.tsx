import {
  Card,
  CardContent,
  type CardProps
} from '@lilypad/ui/components/card';
import { Skeleton } from '@lilypad/ui/components/skeleton';

export function DistrictAvailabilitiesSkeletonCard(
  props: CardProps
): React.JSX.Element {
  return (
    <div className="max-w-2xl rounded-xl border border-border bg-muted/80 dark:bg-muted/20">
      <Card className="gap-2 border-border border-x-0 border-t-0 border-b-xl shadow-none" {...props}>
        <CardContent className="space-y-6">
          <div className="flex flex-col gap-2 md:flex-row">
            <div className="flex flex-1 flex-col space-y-1">
              <Skeleton className="h-4 w-28" />
              <Skeleton className="h-3 w-full" />
            </div>
            <Skeleton className="h-9 w-full md:w-48" />
          </div>

          <div className="space-y-4">
            {Array.from({ length: 7 }).map((_, i) => (
              <div key={i} className="grid grid-cols-8 items-start gap-2">
                <div className="col-span-2 flex items-center space-x-2">
                  <Skeleton className="h-5 w-9 rounded-full" />
                  <Skeleton className="h-4 w-16" />
                </div>

                <div className="col-span-5">
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-10 w-[100px] sm:w-[120px]" />
                    <Skeleton className="h-3 w-2" />
                    <Skeleton className="h-10 w-[100px] sm:w-[120px]" />
                  </div>
                </div>

                <div className="col-span-1">
                  <Skeleton className="size-8 rounded-md" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
      <div className="flex items-center justify-end space-x-2 p-3">
        <Skeleton className="h-8 w-24" />
      </div>
    </div>
  );
}
