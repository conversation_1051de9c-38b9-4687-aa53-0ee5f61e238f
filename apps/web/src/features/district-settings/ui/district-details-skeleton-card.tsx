import {
  Card,
  CardContent,
  type CardProps
} from '@lilypad/ui/components/card';
import { Skeleton } from '@lilypad/ui/components/skeleton';

export function DistrictDetailsSkeletonCard(
  props: CardProps
): React.JSX.Element {
  return (
    <div className="max-w-2xl rounded-xl border border-border bg-muted/80 dark:bg-muted/20">
      <Card className="gap-2 border-border border-x-0 border-t-0 border-b-xl shadow-none" {...props}>
        <CardContent>
          <div className="space-y-6">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {Array.from({ length: 10 }).map((_, i) => (
                <div
                  key={i}
                  className="flex flex-col space-y-2"
                >
                  <Skeleton className="my-1 h-4 w-20" />
                  <Skeleton className="h-8 w-full" />
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
      <div className="flex items-center justify-end space-x-2 p-3">
        <Skeleton className="h-8 w-24" />
      </div>
    </div>
  );
}
