'use client';

import { type TRPCClientError, useTRPC } from '@lilypad/api/client';
import {
  type UpdateDistrictDetailsSchema,
  updateDistrictDetailsSchema,
} from '@lilypad/api/schemas/districts';
import { useZodForm } from '@lilypad/shared/hooks/use-zod-form';
import { toast } from '@lilypad/ui/components/sonner';
import {
  useMutation,
  useQueryClient,
  useSuspenseQuery,
} from '@tanstack/react-query';
import { useCallback } from 'react';

export function useDistrictDetailsForm() {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  const { data: district } = useSuspenseQuery(
    trpc.districts.getDistrictDetails.queryOptions()
  );

  const methods = useZodForm({
    schema: updateDistrictDetailsSchema,
    mode: 'onSubmit',
    defaultValues: {
      name: district.name,
      slug: district.slug,
      type: district.type,
      website: district.website,
      ncesId: district.ncesId,
      stateId: district.stateId,
      county: district.county,
      numSchools: district.numSchools ?? 0,
      numStudents: district.numStudents ?? 0,
      invoiceEmail: district.invoiceEmail,
    },
  });

  const { mutateAsync: updateDistrictDetails, isPending: isSaving } =
    useMutation(
      trpc.districts.updateDistrictDetails.mutationOptions({
        onSuccess: () => {
          toast.success('District details updated successfully');
          queryClient.invalidateQueries({
            queryKey: [['districts', 'getDistrictDetails']],
            exact: false,
          });
        },
        onError: (error: TRPCClientError) => {
          toast.error('Failed to update district details', {
            description: error.message ?? 'An unexpected error occurred',
          });
        },
      })
    );

  const canSubmit =
    methods.formState.isValid && methods.formState.isDirty && !isSaving;

  const onSubmit = useCallback(
    async (data: UpdateDistrictDetailsSchema) => {
      if (!canSubmit) {
        return;
      }
      await updateDistrictDetails(data);
    },
    [canSubmit, updateDistrictDetails]
  );

  return {
    district,
    methods,
    canSubmit,
    onSubmit,
    isSaving,
  };
}
