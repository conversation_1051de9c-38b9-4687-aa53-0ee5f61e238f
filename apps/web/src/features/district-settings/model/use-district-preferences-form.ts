'use client';

import { type TRPCClientError, useTRPC } from '@lilypad/api/client';
import {
  type UpdateDistrictPreferencesSchema,
  updateDistrictPreferencesSchema,
} from '@lilypad/api/schemas/districts';
import { useZodForm } from '@lilypad/shared/hooks/use-zod-form';
import { toast } from '@lilypad/ui/components/sonner';
import {
  useMutation,
  useQueryClient,
  useSuspenseQuery,
} from '@tanstack/react-query';
import { useCallback } from 'react';

export function useDistrictPreferencesForm() {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  const { data: preferences } = useSuspenseQuery(
    trpc.districts.getDistrictPreferences.queryOptions()
  );

  const methods = useZodForm({
    schema: updateDistrictPreferencesSchema,
    mode: 'onSubmit',
    defaultValues: {
      chromebookSetup: preferences?.chromebookSetup ?? false,
      quietSpaceForEvaluation: preferences?.quietSpaceForEvaluation ?? false,
      internetStability: preferences?.internetStability ?? false,
    },
  });

  const { mutateAsync: updateDistrictPreferences, isPending: isSaving } =
    useMutation(
      trpc.districts.updateDistrictPreferences.mutationOptions({
        onSuccess: () => {
          toast.success('District preferences updated successfully');
          queryClient.invalidateQueries({
            queryKey: [['districts', 'getDistrictPreferences']],
            exact: false,
          });
        },
        onError: (error: TRPCClientError) => {
          toast.error('Failed to update district preferences', {
            description: error.message ?? 'An unexpected error occurred',
          });
        },
      })
    );

  const canSubmit =
    methods.formState.isValid && methods.formState.isDirty && !isSaving;

  const onSubmit = useCallback(
    async (data: UpdateDistrictPreferencesSchema) => {
      if (!canSubmit) {
        return;
      }
      await updateDistrictPreferences(data);
    },
    [canSubmit, updateDistrictPreferences]
  );

  return {
    preferences,
    methods,
    canSubmit,
    onSubmit,
    isSaving,
  };
}
