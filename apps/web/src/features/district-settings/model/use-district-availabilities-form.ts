'use client';

import { type TRPCClientError, useTRPC } from '@lilypad/api/client';
import {
  type UpdateDistrictAvailabilitiesSchema,
  updateDistrictAvailabilitiesSchema,
} from '@lilypad/api/schemas/districts';
import { TIMEZONES } from '@lilypad/shared/constants/timezones';
import { useZodForm } from '@lilypad/shared/hooks/use-zod-form';
import { toast } from '@lilypad/ui/components/sonner';
import {
  useMutation,
  useQueryClient,
  useSuspenseQuery,
} from '@tanstack/react-query';
import { useCallback } from 'react';

const DEFAULT_FORM_VALUES: UpdateDistrictAvailabilitiesSchema = {
  availabilitySchedule: {
    monday: { enabled: false, slots: [] },
    tuesday: { enabled: false, slots: [] },
    wednesday: { enabled: false, slots: [] },
    thursday: { enabled: false, slots: [] },
    friday: { enabled: false, slots: [] },
    saturday: { enabled: false, slots: [] },
    sunday: { enabled: false, slots: [] },
  },
  timezone: 'America/New_York',
};

export function useDistrictAvailabilitiesForm() {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  const { data: availabilities } = useSuspenseQuery(
    trpc.districts.getDistrictAvailabilities.queryOptions()
  );

  const methods = useZodForm({
    schema: updateDistrictAvailabilitiesSchema,
    mode: 'onChange',
    defaultValues: availabilities || DEFAULT_FORM_VALUES,
  });

  const handleSuccess = useCallback(async () => {
    toast.success('District availabilities updated successfully');
    await queryClient.invalidateQueries({
      queryKey: [['districts', 'getDistrictAvailabilities']],
      exact: false,
    });
  }, [queryClient]);

  const handleError = useCallback((error: TRPCClientError) => {
    toast.error('Failed to update district availabilities', {
      description: error.message ?? 'An unexpected error occurred',
    });
  }, []);

  const { mutateAsync: updateAvailabilities, isPending: isSaving } =
    useMutation(
      trpc.districts.updateDistrictAvailabilities.mutationOptions({
        onSuccess: handleSuccess,
        onError: handleError,
      })
    );

  const canSubmit =
    methods.formState.isValid && methods.formState.isDirty && !isSaving;

  const onSubmit = useCallback(
    async (values: UpdateDistrictAvailabilitiesSchema) => {
      if (!canSubmit) {
        return;
      }
      await updateAvailabilities(values);
    },
    [canSubmit, updateAvailabilities]
  );

  const timezoneOptions = TIMEZONES.map((tz) => ({
    value: tz.value,
    label: tz.label,
    prefix: tz.prefix,
  }));

  return {
    methods,
    onSubmit,
    canSubmit,
    timezoneOptions,
    isSaving,
  };
}
