'use client';

import NiceModal from '@ebay/nice-modal-react';
import { useTRPC } from '@lilypad/api/client';
import {
  type UpdateDistrictLogoSchema,
  updateDistrictLogoSchema,
} from '@lilypad/api/schemas/districts';
import { FileUploadAction, MAX_IMAGE_SIZE } from '@lilypad/shared/file-upload';
import { useZodForm } from '@lilypad/shared/hooks/use-zod-form';
import { fileToSerializable } from '@lilypad/shared/lib/file-utils';
import { toast } from '@lilypad/ui/components/sonner';
import {
  useMutation,
  useQueryClient,
  useSuspenseQuery,
} from '@tanstack/react-query';
import { useCallback } from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { CropPhotoModal } from '@/shared/ui/crop-photo-modal';
import type { TRPCClientError } from '@lilypad/api/client';

export function useDistrictLogoForm() {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  const { data: district } = useSuspenseQuery(
    trpc.districts.getDistrictLogo.queryOptions()
  );

  const methods = useZodForm({
    schema: updateDistrictLogoSchema,
    mode: 'onSubmit',
    defaultValues: {
      action: FileUploadAction.None,
      imageUrl: district?.logo ?? '',
      image: undefined,
    },
  });

  const { mutateAsync: updateDistrictLogo, isPending: isSaving } = useMutation(
    trpc.districts.updateDistrictLogo.mutationOptions({
      onSuccess: () => {
        toast.success('District logo updated');
        queryClient.invalidateQueries({
          queryKey: [['districts', 'getDistrictLogo']],
        });
      },
      onError: (error: TRPCClientError) => {
        toast.error('Failed to update district logo', {
          description: error.message ?? 'An unexpected error occurred',
        });
      },
    })
  );

  const onSubmit: SubmitHandler<UpdateDistrictLogoSchema> = useCallback(
    async (values) => {
      if (isSaving) {
        return;
      }
      const processedData: UpdateDistrictLogoSchema = {
        action: values.action,
        imageUrl: values.imageUrl,
        image:
          values.image instanceof File
            ? await fileToSerializable(values.image)
            : undefined,
      };

      await updateDistrictLogo(processedData);
    },
    [isSaving, updateDistrictLogo]
  );

  const onDrop = useCallback(
    async (files: File[]): Promise<void> => {
      if (files && files.length > 0) {
        const file = files[0];
        if (file.size > MAX_IMAGE_SIZE) {
          toast.error(
            `Uploaded image shouldn't exceed ${MAX_IMAGE_SIZE / 1_000_000} MB size limit`
          );
        } else {
          const croppedImage: File | null = await NiceModal.show(
            CropPhotoModal,
            {
              file,
              aspectRatio: 1,
              circularCrop: false,
            }
          );
          if (croppedImage) {
            methods.setValue('action', FileUploadAction.Update);
            methods.setValue('image', croppedImage, {
              shouldValidate: true,
              shouldDirty: true,
            });
            await onSubmit({
              action: FileUploadAction.Update,
              imageUrl: district?.logo ?? '',
              image: croppedImage,
            });
          }
        }
      }
    },
    [district?.logo, methods, onSubmit]
  );

  const onRemoveImage = useCallback(async (): Promise<void> => {
    if (!district?.logo) {
      return;
    }

    methods.setValue('action', FileUploadAction.Delete);
    methods.setValue('image', undefined, {
      shouldValidate: true,
      shouldDirty: true,
    });
    await onSubmit({
      action: FileUploadAction.Delete,
      imageUrl: district.logo,
    });
  }, [district?.logo, methods, onSubmit]);

  const canSubmit = !isSaving;

  return {
    district,
    methods,
    canSubmit,
    onSubmit,
    onDrop,
    onRemoveImage,
    isSaving,
  };
}
