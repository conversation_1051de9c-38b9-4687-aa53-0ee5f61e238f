'use client';

import { type TRPCClientError, useTRPC } from '@lilypad/api/client';
import {
  type UpdateDistrictAddressSchema,
  updateDistrictAddressSchema,
} from '@lilypad/api/schemas/districts';
import { useZodForm } from '@lilypad/shared/hooks/use-zod-form';
import { toast } from '@lilypad/ui/components/sonner';
import {
  useMutation,
  useQueryClient,
  useSuspenseQuery,
} from '@tanstack/react-query';
import { useCallback } from 'react';

export function useDistrictAddressForm() {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  const { data: address } = useSuspenseQuery(
    trpc.districts.getDistrictAddress.queryOptions()
  );

  const methods = useZodForm({
    schema: updateDistrictAddressSchema,
    mode: 'onSubmit',
    defaultValues: {
      type: address?.type ?? '',
      address: address?.address ?? '',
      address2: address?.address2 ?? '',
      city: address?.city ?? '',
      state: address?.state ?? '',
      zipcode: address?.zipcode ?? '',
    },
  });

  const { mutateAsync: updateDistrictAddress, isPending: isSaving } =
    useMutation(
      trpc.districts.updateDistrictAddress.mutationOptions({
        onSuccess: () => {
          toast.success('District address updated successfully');
          queryClient.invalidateQueries({
            queryKey: [['districts', 'getDistrictAddress']],
            exact: false,
          });
        },
        onError: (error: TRPCClientError) => {
          toast.error('Failed to update district address', {
            description: error.message ?? 'An unexpected error occurred',
          });
        },
      })
    );

  const canSubmit =
    methods.formState.isValid && methods.formState.isDirty && !isSaving;

  const onSubmit = useCallback(
    async (data: UpdateDistrictAddressSchema) => {
      if (!canSubmit) {
        return;
      }
      await updateDistrictAddress(data);
    },
    [canSubmit, updateDistrictAddress]
  );

  return {
    address,
    methods,
    canSubmit,
    onSubmit,
    isSaving,
  };
}
