import {
  CasePriorityEnumMap,
  CaseStatusEnumMap,
  CaseTypeEnumMap,
} from '@lilypad/db/enums';
import { Badge } from '@lilypad/ui/components/badge';
import { Card, CardContent, CardHeader } from '@lilypad/ui/components/card';
import { cn } from '@lilypad/ui/lib/utils';
import { Building2, Calendar } from 'lucide-react';
import type { CaseTableRow } from '@/entities/cases/model/schema';
import { UserAvatarStack } from '@/shared/ui/users';

interface CaseCardProps {
  data: CaseTableRow;
  index: number;
}

const priorityColors = {
  LOW: 'bg-green-100 text-green-800 border-green-200',
  MEDIUM: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  HIGH: 'bg-orange-100 text-orange-800 border-orange-200',
  URGENT: 'bg-red-100 text-red-800 border-red-200',
};

const statusColors = {
  READY_FOR_EVALUATION: 'bg-blue-100 text-blue-800 border-blue-200',
  EVALUATION_IN_PROGRESS: 'bg-purple-100 text-purple-800 border-purple-200',
  REPORT_IN_PROGRESS: 'bg-indigo-100 text-indigo-800 border-indigo-200',
  AWAITING_MEETING: 'bg-amber-100 text-amber-800 border-amber-200',
  MEETING_COMPLETE: 'bg-green-100 text-green-800 border-green-200',
};

export function CaseCard({ data: caseItem }: CaseCardProps) {
  const isOverdue =
    caseItem.evaluationDueDate &&
    new Date(caseItem.evaluationDueDate) < new Date();

  return (
    <Card className="h-full transition-shadow hover:shadow-md">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between gap-2">
          <div className="min-w-0 flex-1">
            <h4 className="truncate font-semibold text-sm leading-tight">
              {caseItem.studentFullName}
            </h4>
            <p className="text-muted-foreground text-xs">
              Case #{caseItem.id.slice(0, 8)}
            </p>
          </div>
          <div className="flex flex-col items-end gap-1">
            <Badge
              className={cn('text-xs', priorityColors[caseItem.priority])}
              variant="outline"
            >
              {CasePriorityEnumMap[caseItem.priority]}
            </Badge>
            {!caseItem.isActive && (
              <Badge className="text-xs" variant="secondary">
                Inactive
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-3">
        {/* Status and Type */}
        <div className="flex flex-wrap gap-2">
          <Badge
            className={cn('text-xs', statusColors[caseItem.status])}
            variant="outline"
          >
            {CaseStatusEnumMap[caseItem.status]}
          </Badge>
          <Badge className="text-xs" variant="secondary">
            {CaseTypeEnumMap[caseItem.caseType]}
          </Badge>
        </div>

        {/* School Information */}
        {caseItem.schoolName && (
          <div className="flex items-start gap-2">
            <Building2 className="mt-0.5 size-3 text-muted-foreground" />
            <div className="min-w-0 flex-1">
              <p className="truncate font-medium text-xs">
                {caseItem.schoolName}
              </p>
              {caseItem.districtName && (
                <p className="truncate text-muted-foreground text-xs">
                  {caseItem.districtName}
                </p>
              )}
            </div>
          </div>
        )}

        {/* Assigned Users */}
        <UserAvatarStack userNames={caseItem.assignedUserNames} size="sm" />

        {/* Due Date */}
        {caseItem.evaluationDueDate && (
          <div className="flex items-center gap-2">
            <Calendar className="size-3 text-muted-foreground" />
            <span
              className={cn(
                'text-xs',
                isOverdue ? 'font-medium text-red-600' : 'text-muted-foreground'
              )}
            >
              Due {new Date(caseItem.evaluationDueDate).toLocaleDateString()}
            </span>
            {isOverdue && (
              <Badge className="ml-auto text-xs" variant="destructive">
                Overdue
              </Badge>
            )}
          </div>
        )}

        {/* Created Date */}
        <div className="flex items-center gap-2 border-t pt-2">
          <Calendar className="size-3 text-muted-foreground" />
          <span className="text-muted-foreground text-xs">
            Created {new Date(caseItem.createdAt).toLocaleDateString()}
          </span>
        </div>
      </CardContent>
    </Card>
  );
}
