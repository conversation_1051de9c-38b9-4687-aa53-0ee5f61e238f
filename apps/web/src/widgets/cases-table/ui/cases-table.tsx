'use client';

import { useTR<PERSON> } from '@lilypad/api/client';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@lilypad/ui/components/alert';
import { Button } from '@lilypad/ui/components/button';
import { ButtonGroup } from '@lilypad/ui/components/button-group';
import { DataTable } from '@lilypad/ui/data-table/data-table';
import { DataTableAdvancedToolbar } from '@lilypad/ui/data-table/data-table-advanced-toolbar';
import { DataTableFilterMenu } from '@lilypad/ui/data-table/data-table-filter-menu';
import { DataTableSortList } from '@lilypad/ui/data-table/data-table-sort-list';
import { getValidFilters } from '@lilypad/ui/data-table/lib/utils';
import { useSuspenseQuery } from '@tanstack/react-query';
import {
  AlignJustifyIcon,
  InfoIcon,
  LayoutGridIcon,
  PlusIcon,
} from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import React from 'react';

import type { CaseTableRow } from '@/entities/cases/model/schema';
import { casesSearchParamsCache } from '@/entities/cases/model/schema';
import { useDataTable } from '@/shared/hooks/use-data-table';
import { CaseCard } from './case-card';
import { casesTableColumns } from './cases-table-columns';

export function CasesTable() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const trpc = useTRPC();

  const search = React.useMemo(() => {
    const params = Object.fromEntries(searchParams.entries());
    return casesSearchParamsCache.parse(params);
  }, [searchParams]);

  const validFilters = React.useMemo(
    () => getValidFilters(search.filters),
    [search.filters]
  );

  const { data, isError } = useSuspenseQuery(
    trpc.cases.getCases.queryOptions(
      {
        page: search.page,
        perPage: search.perPage,
        search: search.search,
        filters: validFilters,
        joinOperator: search.joinOperator,
        sort: search.sort,
      },
      {
        staleTime: 1000,
      }
    )
  );

  const [showGrid, setShowGrid] = React.useState(false);

  const { table, debounceMs, advancedFilters, setAdvancedFilters } =
    useDataTable({
      data: data?.data ?? [],
      columns: casesTableColumns,
      pageCount: data?.pagination.pages ?? 1,
      shallow: false,
      clearOnDefault: true,
      enableAdvancedFilter: true,
      initialState: {
        pagination: {
          pageIndex: (search.page ?? 1) - 1,
          pageSize: search.perPage ?? 10,
        },
        sorting: search.sort ?? [{ id: 'createdAt', desc: true }],
      },
    });

  const handleRowClick = React.useCallback(
    (caseItem: CaseTableRow) => {
      router.push(`/cases/${caseItem.id}`);
    },
    [router]
  );

  const handleGridToggle = React.useCallback((_showGrid: boolean) => {
    setShowGrid(_showGrid);
  }, []);

  if (isError) {
    return (
      <div className="flex items-center justify-center p-8">
        <Alert variant="destructive">
          <InfoIcon className="h-4 w-4" />
          <AlertTitle>Error loading cases</AlertTitle>
          <AlertDescription>
            There was an error loading the cases. Please try again.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <DataTable
      gridConfig={{
        cardComponent: CaseCard,
        cardProps: (caseData, index) => ({
          data: caseData,
          index,
        }),
      }}
      onRowClick={handleRowClick}
      paginationClassName="px-4 py-2"
      rowClickable={true}
      table={table}
      view={showGrid ? 'grid' : 'table'}
    >
      <DataTableAdvancedToolbar className="border-b px-4 py-2" table={table}>
        <div className="flex items-center gap-2">
          <DataTableSortList align="start" table={table} />
          <DataTableFilterMenu
            debounceMs={debounceMs}
            filters={advancedFilters}
            onFiltersChange={setAdvancedFilters}
            table={table}
          />
        </div>
        <div className="flex items-center gap-2">
          <Button disabled size="sm" variant="outline">
            <PlusIcon className="mr-2 size-4" />
            Add Case
          </Button>
          <ButtonGroup>
            <Button
              className={showGrid ? '' : 'bg-accent'}
              onClick={() => handleGridToggle(false)}
              size="sm"
              variant="outline"
            >
              <AlignJustifyIcon className="size-4" />
            </Button>
            <Button
              className={showGrid ? 'bg-accent' : ''}
              onClick={() => handleGridToggle(true)}
              size="sm"
              variant="outline"
            >
              <LayoutGridIcon className="size-4" />
            </Button>
          </ButtonGroup>
        </div>
      </DataTableAdvancedToolbar>
    </DataTable>
  );
}
