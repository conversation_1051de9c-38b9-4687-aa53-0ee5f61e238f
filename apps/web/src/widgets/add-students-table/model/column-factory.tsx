import type { GenderEnum, SchoolGradeEnum } from '@lilypad/db/schema/enums';
import type { Language } from '@lilypad/db/schema/types';
import type { ColumnDef } from '@tanstack/react-table';
import type * as React from 'react';
import type { StudentRowData } from '@/features/add-students/model/schema';
import { GenderSelectCell } from '../ui/cells/gender-select-cell';
import { GradeSelectCell } from '../ui/cells/grade-select-cell';
import { SchoolSelectCell } from '../ui/cells/school-select-cell';
import {
  MemoizedActionsCell,
  MemoizedDateCell,
  MemoizedDocumentsCell,
  MemoizedLanguageCell,
  MemoizedParentsCell,
  MemoizedSelectCell,
  MemoizedStudentInputCell,
  SelectionCell,
  SelectionHeader,
} from '../ui/memoized-cells';

export function createStudentColumns(
  schools: Array<{ id: string; name: string }>,
  languages: Pick<Language, 'id' | 'name' | 'emoji'>[],
  handlers: {
    handleKeyDown: (
      e: React.KeyboardEvent,
      rowIndex: number,
      columnIndex: number
    ) => void;
    setParentDialogState: (state: {
      open: boolean;
      studentId: string | null;
    }) => void;
  }
): ColumnDef<StudentRowData>[] {
  return [
    createSelectionColumn(),
    createTextColumn(
      'studentIdNumber',
      'Student ID',
      1,
      'Enter ID',
      handlers.handleKeyDown
    ),
    createTextColumn(
      'firstName',
      'First Name',
      2,
      'First',
      handlers.handleKeyDown
    ),
    createTextColumn(
      'middleName',
      'Middle Name',
      3,
      'Middle',
      handlers.handleKeyDown
    ),
    createTextColumn(
      'lastName',
      'Last Name',
      4,
      'Last',
      handlers.handleKeyDown
    ),
    createTextColumn(
      'preferredName',
      'Preferred Name',
      5,
      'Preferred',
      handlers.handleKeyDown
    ),
    createDateColumn('dateOfBirth', 'Date of Birth'),
    createGenderColumn(),
    createGradeColumn(),
    createSchoolColumn(schools),
    createLanguageColumn(languages),
    createParentsColumn(handlers.setParentDialogState),
    createDocumentsColumn(),
    createActionsColumn(),
  ];
}

function createSelectionColumn(): ColumnDef<StudentRowData> {
  return {
    id: 'select',
    header: () => <SelectionHeader />,
    cell: ({ row }) => <SelectionCell studentId={row.original.id} />,
    enableSorting: false,
    enableHiding: false,
  };
}

function createTextColumn(
  field: keyof StudentRowData,
  header: string,
  colIndex: number,
  placeholder: string,
  handleKeyDown: (
    e: React.KeyboardEvent,
    rowIndex: number,
    columnIndex: number
  ) => void
): ColumnDef<StudentRowData> {
  return {
    accessorKey: field,
    header,
    cell: ({ row }) => (
      <MemoizedStudentInputCell
        dataCol={colIndex.toString()}
        dataRow={row.index}
        field={field}
        onKeyDown={(e) => handleKeyDown(e, row.index, colIndex)}
        placeholder={placeholder}
        studentId={row.original.id}
      />
    ),
  };
}

function createDateColumn(
  field: keyof StudentRowData,
  header: string
): ColumnDef<StudentRowData> {
  return {
    accessorKey: field,
    header,
    cell: ({ row }) => (
      <MemoizedDateCell field={field} studentId={row.original.id} />
    ),
  };
}

function createGenderColumn(): ColumnDef<StudentRowData> {
  return {
    accessorKey: 'gender',
    header: 'Gender',
    cell: ({ row }) => (
      <MemoizedSelectCell
        field="gender"
        renderCell={({ value, onChange, hasError }) => (
          <GenderSelectCell
            hasError={hasError}
            onChange={onChange}
            value={value as GenderEnum}
          />
        )}
        studentId={row.original.id}
      />
    ),
  };
}

function createGradeColumn(): ColumnDef<StudentRowData> {
  return {
    accessorKey: 'grade',
    header: 'Grade',
    cell: ({ row }) => (
      <MemoizedSelectCell
        field="grade"
        renderCell={({ value, onChange, hasError }) => (
          <GradeSelectCell
            hasError={hasError}
            onChange={onChange}
            value={value as SchoolGradeEnum}
          />
        )}
        studentId={row.original.id}
      />
    ),
  };
}

function createSchoolColumn(
  schools: Array<{ id: string; name: string }>
): ColumnDef<StudentRowData> {
  return {
    accessorKey: 'primarySchoolId',
    header: 'School',
    cell: ({ row }) => (
      <MemoizedSelectCell
        field="primarySchoolId"
        renderCell={({ value, onChange, hasError }) => (
          <SchoolSelectCell
            hasError={hasError}
            onChange={onChange}
            schools={schools}
            value={value as string}
          />
        )}
        studentId={row.original.id}
      />
    ),
  };
}

function createLanguageColumn(
  languages: Pick<Language, 'id' | 'name' | 'emoji'>[]
): ColumnDef<StudentRowData> {
  return {
    accessorKey: 'languageIds',
    header: 'Languages',
    cell: ({ row }) => (
      <MemoizedLanguageCell languages={languages} studentId={row.original.id} />
    ),
  };
}

function createParentsColumn(
  setParentDialogState: (state: {
    open: boolean;
    studentId: string | null;
  }) => void
): ColumnDef<StudentRowData> {
  return {
    accessorKey: 'parents',
    header: 'Parents/Guardians',
    cell: ({ row }) => (
      <MemoizedParentsCell
        onOpenDialog={setParentDialogState}
        studentId={row.original.id}
      />
    ),
  };
}

function createDocumentsColumn(): ColumnDef<StudentRowData> {
  return {
    accessorKey: 'documents',
    header: 'Documents',
    cell: ({ row }) => <MemoizedDocumentsCell studentId={row.original.id} />,
  };
}

function createActionsColumn(): ColumnDef<StudentRowData> {
  return {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => <MemoizedActionsCell studentId={row.original.id} />,
  };
}
