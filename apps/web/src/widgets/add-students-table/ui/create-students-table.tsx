'use client';

import { useTRPC } from '@lilypad/api/client';
import type { ParentSchema } from '@lilypad/api/schemas/students';
import { If } from '@lilypad/ui/components/if';
import { Skeleton } from '@lilypad/ui/components/skeleton';
import { toast } from '@lilypad/ui/components/sonner';
import { useSuspenseQuery } from '@tanstack/react-query';
import { getCoreRowModel, useReactTable } from '@tanstack/react-table';
import React from 'react';
import { useHotkeys } from 'react-hotkeys-hook';
import {
  useAllStudents,
  useBatchOperations,
  useSelectionStore,
  useStudentOperations,
  useStudentsCount,
  useStudentsDataStore,
  useStudentsUIStore,
} from '@/features/add-students/model';
import { useBulkCreateStudents } from '@/features/add-students/model/hooks/use-bulk-create-students';
import type { StudentRowData } from '@/features/add-students/model/schema';
import { convertStudentRowForSave } from '@/features/add-students/model/schema';
import { useValidationStore } from '@/features/add-students/model/stores/students-validation.store';
import { CSVImportDialog } from '@/features/add-students/ui/csv-upload/csv-import-dialog';
import { ParentDialog } from '@/features/add-students/ui/parents-dialog';
import { createStudentColumns } from '../model/column-factory';
import type { DuplicateGroup, ParentDialogState } from '../model/types';
import { useStudentsTableHandlers } from '../model/use-students-table-handlers';
import { DuplicateStudentsDialog } from './duplicate-students-dialog';
import { SaveSummaryDialog } from './save-summary-dialog';
import { StudentsTableToolbar } from './students-table-toolbar';
import { StudentsTableVirtualized } from './students-table-virtualized';

export function CreateStudentsTable() {
  const trpc = useTRPC();

  const { data: schools } = useSuspenseQuery(
    trpc.schools.getSchools.queryOptions()
  );
  const { data: languages } = useSuspenseQuery(
    trpc.languages.getAllLanguages.queryOptions()
  );

  const [parentDialogState, setParentDialogState] =
    React.useState<ParentDialogState>({
      open: false,
      studentId: null,
    });
  const [showSummary, setShowSummary] = React.useState(false);
  const [showCSVImport, setShowCSVImport] = React.useState(false);
  const [duplicateDialogState, setDuplicateDialogState] = React.useState<{
    open: boolean;
    duplicateGroups: DuplicateGroup[];
  }>({
    open: false,
    duplicateGroups: [],
  });

  // Use granular store hooks
  const students = useAllStudents();
  const _studentsCount = useStudentsCount();
  const selectedCount = useSelectionStore((state) => state.selectedIds.size);
  const _hasHydrated = useStudentsDataStore((state) => state._hasHydrated);

  // UI state
  const { saveResults, setSaveResults, clearSaveResults } =
    useStudentsUIStore();

  // Operations
  const { addStudent, updateStudent } = useStudentOperations();
  const { deleteSelectedRows, duplicateSelectedRows, validateAll } =
    useBatchOperations();
  const reset = useStudentsDataStore((state) => state.reset);
  const importStudents = useStudentsDataStore((state) => state.importStudents);

  const { bulkCreateStudents, isSaving } = useBulkCreateStudents({
    onSuccess: (results) => {
      setSaveResults(results);
      setShowSummary(true);
    },
    onError: () => {
      setShowSummary(true);
    },
  });

  const handleShowDuplicateDialog = (duplicateGroups: DuplicateGroup[]) => {
    setDuplicateDialogState({
      open: true,
      duplicateGroups,
    });
  };

  const { handleKeyDown, handleSaveAll, handleRemoveDuplicatesAndSave } =
    useStudentsTableHandlers({
      students,
      validateAllRows: validateAll,
      clearSaveResults,
      executeBulkCreate: async (data) => {
        const convertedStudents = await Promise.all(
          data.students.map(convertStudentRowForSave)
        );

        bulkCreateStudents({ students: convertedStudents });
      },
      addRow: addStudent,
      columnsLength: 14,
      onShowDuplicateDialog: handleShowDuplicateDialog,
    });

  const columns = React.useMemo(
    () =>
      createStudentColumns(schools, languages, {
        handleKeyDown,
        setParentDialogState,
      }),
    [schools, languages, handleKeyDown]
  );

  const table = useReactTable({
    data: students,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getRowId: (row) => row.id,
    enableRowSelection: true,
  });

  useHotkeys('mod+k', () => addStudent());

  const currentParentStudent = students.find(
    (s) => s.id === parentDialogState.studentId
  );

  const handleCSVImport = (
    importedStudents: StudentRowData[],
    replaceExisting: boolean
  ) => {
    importStudents(importedStudents, replaceExisting);
    setShowCSVImport(false);

    toast.success(
      `Successfully imported ${importedStudents.length} student${
        importedStudents.length !== 1 ? 's' : ''
      }`
    );
  };

  const handleClearAll = React.useCallback(() => {
    useStudentsDataStore.persist.clearStorage();
    useSelectionStore.getState().clearSelection();
    useValidationStore.getState().clearAllErrors();
    reset();
  }, [reset]);

  const onReset = React.useCallback(() => {
    reset();
    clearSaveResults();
  }, [clearSaveResults, reset]);

  const onSaveParents = React.useCallback(
    (parents: ParentSchema[]) => {
      if (currentParentStudent) {
        updateStudent(currentParentStudent.id, {
          parents: parents.map((p) => ({
            ...p,
            isPrimaryContact: p.isPrimaryContact ?? false,
            hasPickupAuthorization: p.hasPickupAuthorization ?? false,
          })),
        });
      }
    },
    [currentParentStudent, updateStudent]
  );

  if (!_hasHydrated) {
    return (
      <div className="flex h-full flex-col space-y-4 p-4">
        <Skeleton className="h-12 w-full" />
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  return (
    <div className="flex h-full flex-col">
      <StudentsTableToolbar
        isSaving={isSaving}
        onAddRow={addStudent}
        onClearAll={handleClearAll}
        onDeleteSelected={deleteSelectedRows}
        onDuplicateSelected={duplicateSelectedRows}
        onImportCSV={() => setShowCSVImport(true)}
        onSaveAll={handleSaveAll}
        selectedRowsCount={selectedCount}
      />

      <StudentsTableVirtualized columnsCount={columns.length} table={table} />

      {/* Parent Dialog */}
      <If condition={currentParentStudent}>
        <ParentDialog
          onOpenChange={(open) =>
            setParentDialogState({ open, studentId: null })
          }
          onSave={onSaveParents}
          open={parentDialogState.open}
          parents={currentParentStudent?.parents || []}
        />
      </If>

      {/* CSV Import Dialog */}
      <CSVImportDialog
        languages={languages}
        onImport={handleCSVImport}
        onOpenChange={setShowCSVImport}
        open={showCSVImport}
        schools={schools}
      />

      {/* Duplicate Students Dialog */}
      <DuplicateStudentsDialog
        duplicateGroups={duplicateDialogState.duplicateGroups}
        onOpenChange={(open) =>
          setDuplicateDialogState((prev) => ({ ...prev, open }))
        }
        onRemoveDuplicatesAndSave={() => {
          handleRemoveDuplicatesAndSave();
          setDuplicateDialogState({ open: false, duplicateGroups: [] });
        }}
        open={duplicateDialogState.open}
      />

      {/* Save Summary Dialog */}
      <SaveSummaryDialog
        onOpenChange={setShowSummary}
        onReset={onReset}
        open={showSummary}
        results={saveResults}
        students={students}
      />
    </div>
  );
}
