'use client';

import {
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Table as UITable,
} from '@lilypad/ui/components/table';
import { cn } from '@lilypad/ui/lib/utils';
import { flexRender, type Row, type Table } from '@tanstack/react-table';
import {
  useVirtualizer,
  type VirtualItem,
  type Virtualizer,
} from '@tanstack/react-virtual';
import React from 'react';
import type { StudentRowData } from '@/features/add-students/model/schema';
import { useSelectionStore } from '@/features/add-students/model/stores/students-selection.store';

interface StudentsTableVirtualizedProps {
  table: Table<StudentRowData>;
  columnsCount: number;
}

export function StudentsTableVirtualized({
  table,
  columnsCount,
}: StudentsTableVirtualizedProps) {
  const tableContainerRef = React.useRef<HTMLDivElement>(null);

  const selectedRows = useSelectionStore((state) => state.selectedIds);

  const { rows } = table.getRowModel();

  const rowVirtualizer = useVirtualizer({
    count: rows.length,
    getScrollElement: () => tableContainerRef.current,
    estimateSize: () => 40, // Estimated row height
    overscan: 10, // Number of rows to render outside of viewport
    // Ensure minimum size for small datasets
    measureElement:
      rows.length < 5
        ? undefined
        : (element) => element.getBoundingClientRect().height,
  });

  const virtualRows = rowVirtualizer.getVirtualItems();
  const totalHeight = rowVirtualizer.getTotalSize();

  const paddingTop = virtualRows.length > 0 ? virtualRows[0].start || 0 : 0;
  const paddingBottom =
    virtualRows.length > 0 ? totalHeight - (virtualRows.at(-1)?.end || 0) : 0;

  // Ensure minimum height for small datasets
  const minHeight = rows.length < 10 ? `${rows.length * 40 + 50}px` : undefined;

  if (rows.length === 0) {
    return (
      <div className="relative flex flex-1 flex-col overflow-hidden">
        <div className="flex-1 overflow-auto">
          <UITable>
            <TableHeader className="sticky top-0 z-10 bg-background">
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead
                      className={cn(
                        'bg-background',
                        header.column.getCanSort() &&
                          'cursor-pointer select-none'
                      )}
                      key={header.id}
                      onClick={header.column.getToggleSortingHandler()}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell
                  className="h-24 text-center text-muted-foreground"
                  colSpan={columnsCount}
                >
                  No students added. Click "Add Row" to get started.
                </TableCell>
              </TableRow>
            </TableBody>
          </UITable>
        </div>
      </div>
    );
  }

  return (
    <div className="relative flex flex-1 flex-col overflow-hidden">
      <div
        className="flex-1 overflow-auto"
        ref={tableContainerRef}
        style={{ minHeight }}
      >
        <UITable>
          <TableHeader className="sticky top-0 z-10 bg-background">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead
                    className={cn(
                      'bg-background',
                      header.column.getCanSort() && 'cursor-pointer select-none'
                    )}
                    key={header.id}
                    onClick={header.column.getToggleSortingHandler()}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {paddingTop > 0 && (
              <tr>
                <td style={{ height: paddingTop }} />
              </tr>
            )}
            {virtualRows.map((virtualRow) => {
              const row = rows[virtualRow.index];
              return (
                <MemoizedTableRow
                  key={row.id}
                  row={row}
                  rowVirtualizer={rowVirtualizer}
                  selectedRows={selectedRows}
                  virtualRow={virtualRow}
                />
              );
            })}
            {paddingBottom > 0 && (
              <tr>
                <td style={{ height: paddingBottom }} />
              </tr>
            )}
          </TableBody>
        </UITable>
      </div>
    </div>
  );
}

interface MemoizedTableRowProps {
  row: Row<StudentRowData>;
  virtualRow: VirtualItem;
  selectedRows: Set<string>;
  rowVirtualizer: Virtualizer<HTMLDivElement, Element>;
}

const MemoizedTableRow = React.memo(
  function MemoizedTableRowComponent({
    row,
    virtualRow,
    selectedRows,
    rowVirtualizer,
  }: MemoizedTableRowProps) {
    const isSelected = selectedRows.has(row.original.id);
    const hasErrors = row.original.hasErrors;

    return (
      <TableRow
        className={cn(
          'group',
          isSelected && 'bg-muted/50',
          hasErrors && 'bg-destructive/5'
        )}
        data-index={virtualRow.index}
        ref={rowVirtualizer.measureElement}
      >
        {row.getVisibleCells().map((cell) => (
          <TableCell
            className={cn(
              'px-2 py-2',
              cell.column.id === 'select' && 'w-12',
              cell.column.id === 'actions' && 'w-20'
            )}
            key={cell.id}
          >
            {flexRender(cell.column.columnDef.cell, cell.getContext())}
          </TableCell>
        ))}
      </TableRow>
    );
  },
  (prevProps, nextProps) => {
    // Only re-render if the row data actually changed
    const prevSelected = prevProps.selectedRows.has(prevProps.row.original.id);
    const nextSelected = nextProps.selectedRows.has(nextProps.row.original.id);
    const prevHasErrors = prevProps.row.original.hasErrors;
    const nextHasErrors = nextProps.row.original.hasErrors;

    return (
      prevProps.row.id === nextProps.row.id &&
      prevProps.virtualRow.index === nextProps.virtualRow.index &&
      prevSelected === nextSelected &&
      prevHasErrors === nextHasErrors
    );
  }
);
