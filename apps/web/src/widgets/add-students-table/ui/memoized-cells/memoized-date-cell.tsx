import { DatePicker } from '@lilypad/ui/components/date-picker';
import { cn } from '@lilypad/ui/lib/utils';
import React from 'react';
import {
  useStudentField,
  useStudentOperations,
} from '@/features/add-students/model/hooks/use-student-data';
import type { StudentRowData } from '@/features/add-students/model/schema';
import { useValidationStore } from '@/features/add-students/model/stores/students-validation.store';

interface MemoizedDateCellProps {
  studentId: string;
  field: keyof StudentRowData;
}

export const MemoizedDateCell = React.memo(function MemoizedDateCellComponent({
  studentId,
  field,
}: MemoizedDateCellProps) {
  const { updateStudent } = useStudentOperations();
  const value = useStudentField(studentId, field) as Date;
  const errors = useValidationStore((state) => state.getErrors(studentId));
  const hasError = !!errors?.[field];

  return (
    <DatePicker
      className={cn('h-8 w-32', hasError && 'border-destructive')}
      date={value}
      setDate={(date: Date | undefined) =>
        date && updateStudent(studentId, { [field]: date })
      }
    />
  );
});
