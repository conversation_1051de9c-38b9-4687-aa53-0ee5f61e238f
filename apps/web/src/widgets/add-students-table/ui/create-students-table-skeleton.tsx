/** biome-ignore-all lint/style/noNestedTernary: Fix later */
import { But<PERSON> } from '@lilypad/ui/components/button';
import { Kbd, Kbd<PERSON>ey } from '@lilypad/ui/components/kbd';
import { Skeleton } from '@lilypad/ui/components/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@lilypad/ui/components/table';
import {
  CloudUploadIcon,
  PlusIcon,
  Trash2Icon,
  UploadIcon,
} from 'lucide-react';
import { memo } from 'react';

const COLUMN_HEADERS = [
  { key: 'select', label: '', width: 'w-12' },
  { key: 'studentId', label: 'Student ID', width: 'w-24' },
  { key: 'firstName', label: 'First Name', width: 'w-24' },
  { key: 'middleName', label: 'Middle Name', width: 'w-24' },
  { key: 'lastName', label: 'Last Name', width: 'w-24' },
  { key: 'preferredName', label: 'Preferred Name', width: 'w-24' },
  { key: 'dateOfBirth', label: 'Date of Birth', width: 'w-28' },
  { key: 'gender', label: 'Gender', width: 'w-20' },
  { key: 'grade', label: 'Grade', width: 'w-16' },
  { key: 'school', label: 'School', width: 'w-32' },
  { key: 'languages', label: 'Languages', width: 'w-28' },
  { key: 'parents', label: 'Parents/Guardians', width: 'w-32' },
  { key: 'documents', label: 'Documents', width: 'w-24' },
  { key: 'actions', label: 'Actions', width: 'w-20' },
];

export const CreateStudentsTableSkeleton = memo(() => {
  const ROWS = 13;
  return (
    <div className="flex h-full flex-col">
      <div className="flex items-center justify-between border-b p-4">
        <div className="flex items-center gap-2">
          <Button
            className="flex items-center gap-2"
            disabled
            size="sm"
            variant="outline"
          >
            <PlusIcon className="size-4" />
            <span>Add Row</span>
            <Kbd size="sm">
              <KbdKey>⌘</KbdKey>
              <KbdKey>K</KbdKey>
            </Kbd>
          </Button>

          <Button
            className="flex items-center gap-2"
            disabled
            size="sm"
            variant="outline"
          >
            <UploadIcon className="size-4" />
            <span>Import CSV</span>
          </Button>

          <Button disabled size="sm" variant="cancel">
            <Trash2Icon className="mr-2 size-4" />
            Clear All
          </Button>
        </div>

        <Button disabled size="sm">
          <CloudUploadIcon className="mr-2 size-4" />
          Save All
        </Button>
      </div>

      <div className="relative flex flex-1 flex-col overflow-hidden">
        <div className="flex-1 overflow-auto">
          <Table className="w-full">
            <TableHeader className="sticky top-0 z-10 bg-background">
              <TableRow>
                {COLUMN_HEADERS.map((column) => (
                  <TableHead
                    className={`bg-background ${column.width}`}
                    key={column.key}
                  >
                    {column.label}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>

            <TableBody>
              {Array.from({ length: ROWS }).map((_, rowIndex) => (
                <TableRow className="py-4" key={rowIndex}>
                  {/** biome-ignore lint/complexity/noExcessiveCognitiveComplexity: Fix later */}
                  {COLUMN_HEADERS.map((column, colIndex) => (
                    <TableCell
                      className={`p-2 ${column.width}`}
                      key={`${rowIndex}-${colIndex}`}
                    >
                      {column.key === 'select' ? (
                        <Skeleton className="size-4 rounded" />
                      ) : column.key === 'actions' ? (
                        <Skeleton className="size-8 rounded" />
                      ) : column.key === 'parents' ||
                        column.key === 'documents' ? (
                        <Skeleton className="h-8 w-16 rounded" />
                      ) : column.key === 'languages' ? (
                        <div className="flex gap-1">
                          <Skeleton className="h-8 w-12 rounded-full" />
                          <Skeleton className="h-8 w-10 rounded-full" />
                        </div>
                      ) : column.key === 'gender' ||
                        column.key === 'grade' ||
                        column.key === 'school' ? (
                        <Skeleton className="h-8 w-full rounded" />
                      ) : column.key === 'dateOfBirth' ? (
                        <Skeleton className="h-8 w-20 rounded" />
                      ) : (
                        <Skeleton className="h-8 w-full rounded" />
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
});
CreateStudentsTableSkeleton.displayName = 'CreateStudentsTableSkeleton';
