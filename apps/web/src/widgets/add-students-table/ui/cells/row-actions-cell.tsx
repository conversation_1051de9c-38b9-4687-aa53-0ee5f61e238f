import { Button } from '@lilypad/ui/components/button';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@lilypad/ui/components/tooltip';
import { CopyIcon, Trash2Icon } from 'lucide-react';
import React from 'react';

interface RowActionsCellProps {
  onDuplicate: () => void;
  onDelete: () => void;
}

export const RowActionsCell = React.memo(function RowActionsCellComponent({
  onDuplicate,
  onDelete,
}: RowActionsCellProps) {
  return (
    <div className="flex items-center gap-1">
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            className="h-8 w-8"
            onClick={onDuplicate}
            size="icon"
            variant="ghost"
          >
            <CopyIcon className="size-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Duplicate</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            className="h-8 w-8 text-destructive hover:text-destructive"
            onClick={onDelete}
            size="icon"
            variant="ghost"
          >
            <Trash2Icon className="size-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Delete</TooltipContent>
      </Tooltip>
    </div>
  );
});
