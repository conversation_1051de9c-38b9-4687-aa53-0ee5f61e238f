'use client';

import type { School } from '@lilypad/db/types';
import { Button } from '@lilypad/ui/components/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@lilypad/ui/components/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@lilypad/ui/components/popover';
import { cn } from '@lilypad/ui/lib/utils';
import { CheckIcon, ChevronsUpDownIcon, SchoolIcon } from 'lucide-react';
import React from 'react';

interface SchoolSelectCellProps {
  value: string;
  onChange: (value: string) => void;
  schools: Pick<School, 'id' | 'name'>[];
  hasError?: boolean;
}

export const SchoolSelectCell = React.memo(function SchoolSelectCellComponent({
  value,
  onChange,
  schools,
  hasError,
}: SchoolSelectCellProps) {
  const [open, setOpen] = React.useState(false);

  const selectedSchool = React.useMemo(
    () => schools.find((school) => school.id === value),
    [value, schools]
  );

  const handleSelect = (schoolId: string) => {
    onChange(schoolId);
    setOpen(false);
  };

  return (
    <Popover modal={true} onOpenChange={setOpen} open={open}>
      <PopoverTrigger asChild>
        <Button
          aria-expanded={open}
          aria-label="Select school"
          className={cn(
            'w-fit justify-between',
            hasError && 'border-destructive'
          )}
          role="combobox"
          size="sm"
          variant="outline"
        >
          {selectedSchool ? (
            <span className="truncate text-sm">{selectedSchool.name}</span>
          ) : (
            <span className="font-normal text-muted-foreground">
              Select school
            </span>
          )}
          <ChevronsUpDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent align="start" className="w-80 p-0">
        <Command>
          <CommandInput placeholder="Search schools..." />
          <CommandList>
            <CommandEmpty>No school found.</CommandEmpty>
            <CommandGroup>
              {schools.map((school) => (
                <CommandItem
                  key={school.id}
                  onSelect={() => handleSelect(school.id)}
                  value={school.name}
                >
                  <CheckIcon
                    className={cn(
                      'mr-2 h-4 w-4',
                      value === school.id ? 'opacity-100' : 'opacity-0'
                    )}
                  />
                  <SchoolIcon className="mr-2 h-4 w-4 text-muted-foreground" />
                  <span className="truncate">{school.name}</span>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
});
