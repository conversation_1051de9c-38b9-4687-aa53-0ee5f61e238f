import { type SchoolGradeEnum, SchoolGradeEnumMap } from '@lilypad/db/enums';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@lilypad/ui/components/select';
import { cn } from '@lilypad/ui/lib/utils';
import React from 'react';
import { SchoolGradeBadge } from '@/shared/ui/students/school-grade-badge';

interface GradeSelectCellProps {
  value: SchoolGradeEnum;
  onChange: (value: SchoolGradeEnum) => void;
  hasError?: boolean;
}

const gradeOptions = Object.entries(SchoolGradeEnumMap).map(([key, value]) => ({
  label: value,
  value: key,
}));

export const GradeSelectCell = React.memo(function GradeSelectCellComponent({
  value,
  onChange,
  hasError,
}: GradeSelectCellProps) {
  return (
    <Select onValueChange={onChange} value={value}>
      <SelectTrigger
        className={cn('h-8 w-fit', hasError && 'border-destructive')}
      >
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        {gradeOptions.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            <SchoolGradeBadge grade={option.value as SchoolGradeEnum} />
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
});
