'use client';

import { DocumentCategoryEnum } from '@lilypad/db/enums';
import { Button } from '@lilypad/ui/components/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@lilypad/ui/components/dialog';
import { If } from '@lilypad/ui/components/if';
import { ScrollArea } from '@lilypad/ui/components/scroll-area';
import { toast } from '@lilypad/ui/components/sonner';
import { cn } from '@lilypad/ui/lib/utils';
import {
  FileIcon,
  FileSpreadsheetIcon,
  FileTextIcon,
  FileTypeIcon,
  PaperclipIcon,
  UploadIcon,
  XIcon,
} from 'lucide-react';
import React, { useRef, useState } from 'react';
import type { DocumentSchema } from '@lilypad/api/schemas/students';

interface DocumentUploadCellProps {
  value: DocumentSchema[];
  onChange: (value: DocumentSchema[]) => void;
}

const ALLOWED_FILE_TYPES = [
  'application/pdf',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'text/plain',
];

const ALLOWED_EXTENSIONS = ['.pdf', '.xls', '.xlsx', '.doc', '.docx', '.txt'];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

export const DocumentUploadCell = React.memo(
  function DocumentUploadCellComponent({
    value,
    onChange,
  }: DocumentUploadCellProps) {
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const [dragActive, setDragActive] = React.useState(false);

    const getFileIcon = (
      file: File | { type: string; name: string; size: number; data: string }
    ) => {
      const ext = file.name.split('.').pop()?.toLowerCase();
      switch (ext) {
        case 'pdf':
          return <FileIcon className="size-4" />;
        case 'xls':
        case 'xlsx':
          return <FileSpreadsheetIcon className="size-4" />;
        case 'doc':
        case 'docx':
          return <FileTypeIcon className="size-4" />;
        case 'txt':
          return <FileTextIcon className="size-4" />;
        default:
          return <PaperclipIcon className="size-4" />;
      }
    };

    const formatFileSize = (bytes: number) => {
      if (bytes === 0) {
        return '0 Bytes';
      }
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
    };

    const validateFile = (file: File): string | null => {
      const ext = `.${file.name.split('.').pop()?.toLowerCase()}`;

      if (
        !(
          ALLOWED_EXTENSIONS.includes(ext) ||
          ALLOWED_FILE_TYPES.includes(file.type)
        )
      ) {
        return 'Invalid file type. Allowed: PDF, XLS, XLSX, DOC, DOCX, TXT';
      }

      if (file.size > MAX_FILE_SIZE) {
        return `File too large. Maximum size: ${formatFileSize(MAX_FILE_SIZE)}`;
      }

      return null;
    };

    const handleFileSelect = (files: FileList) => {
      const newDocuments: DocumentSchema[] = [];
      const errors: string[] = [];

      for (const file of Array.from(files)) {
        const error = validateFile(file);
        if (error) {
          errors.push(`${file.name}: ${error}`);
        } else {
          newDocuments.push({
            file,
            category: DocumentCategoryEnum.BACKGROUND,
          });
        }
      }

      if (errors.length > 0) {
        toast.error(errors.join('\n'));
      }

      if (newDocuments.length > 0) {
        onChange([...value, ...newDocuments]);
      }
    };

    const handleDrop = (e: React.DragEvent) => {
      e.preventDefault();
      setDragActive(false);

      if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
        handleFileSelect(e.dataTransfer.files);
      }
    };

    const handleDragOver = (e: React.DragEvent) => {
      e.preventDefault();
      setDragActive(true);
    };

    const handleDragLeave = () => {
      setDragActive(false);
    };

    const removeDocument = (index: number) => {
      const newDocs = [...value];
      newDocs.splice(index, 1);
      onChange(newDocs);
    };

    return (
      <Dialog onOpenChange={setIsDialogOpen} open={isDialogOpen}>
        <DialogTrigger asChild>
          <Button
            className="h-8"
            onClick={() => setIsDialogOpen(true)}
            size="sm"
            variant="outline"
          >
            <FileTextIcon className="mr-2 size-4" />
            {value.length > 0 ? `${value.length} Files` : 'Upload'}
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Upload Documents</DialogTitle>
            <DialogDescription>
              Upload student documents. Maximum 5MB per file.
              <br />
              <strong>Allowed formats:</strong> PDF, Excel, Word, Text
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Drop Zone */}
            {/** biome-ignore lint/a11y/noStaticElementInteractions: Fix later */}
            {/** biome-ignore lint/nursery/noNoninteractiveElementInteractions: Fix later */}
            <div
              className={cn(
                'rounded-lg border border-dashed p-8 text-center transition-colors',
                dragActive ? 'border-primary bg-primary/5' : 'border-border',
                'cursor-pointer hover:border-primary hover:bg-primary/5'
              )}
              onClick={() => fileInputRef.current?.click()}
              onDragLeave={handleDragLeave}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
            >
              <UploadIcon className="mx-auto mb-2 size-8 text-muted-foreground" />
              <p className="font-medium text-sm">
                Drag and drop files or click to browse
              </p>
              <p className="mt-1 text-muted-foreground text-xs">
                PDF, Excel, Word, or Text files up to 5MB
              </p>
              <input
                accept={ALLOWED_EXTENSIONS.join(',')}
                className="hidden"
                multiple
                onChange={(e) =>
                  e.target.files && handleFileSelect(e.target.files)
                }
                ref={fileInputRef}
                type="file"
              />
            </div>

            {/* File List */}
            <If condition={value.length > 0}>
              <div className="space-y-2">
                <p className="font-medium text-sm">
                  Uploaded Documents{' '}
                  <span className="rounded-md bg-muted px-2 py-1 text-muted-foreground text-xs">
                    {value.length}
                    {value.length === 1 ? ' document' : ' documents'}
                  </span>
                </p>
                <ScrollArea className="h-[200px]">
                  <div className="space-y-2">
                    {value.map((doc, index) => (
                      <div
                        className="flex items-center justify-between rounded-lg border p-2 hover:bg-accent/50"
                        key={index}
                      >
                        <div className="flex min-w-0 flex-1 items-center gap-2">
                          <span className="text-lg">
                            {getFileIcon(doc.file)}
                          </span>
                          <div className="min-w-0 flex-1">
                            <p className="truncate font-medium text-sm">
                              {doc.file.name}
                            </p>
                            <p className="text-muted-foreground text-xs">
                              {formatFileSize(doc.file.size)}
                            </p>
                          </div>
                        </div>
                        <Button
                          className="size-8 p-0"
                          onClick={() => removeDocument(index)}
                          size="sm"
                          variant="ghost"
                        >
                          <XIcon className="size-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            </If>
          </div>

          <DialogFooter>
            <Button onClick={() => setIsDialogOpen(false)} variant="outline">
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }
);
