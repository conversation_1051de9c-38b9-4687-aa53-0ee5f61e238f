import { Card, CardContent } from '@lilypad/ui/components/card';
import { WorkflowIcon } from 'lucide-react';

export function CaseWorkflowNotFound() {
  return (
    <div className="flex h-[90vh] flex-col items-center justify-center p-8">
      <Card className="max-w-md">
        <CardContent className="flex flex-col items-center p-8 text-center">
          <WorkflowIcon className="mb-4 size-12 text-muted-foreground" />
          <h3 className="mb-2 font-semibold text-lg">No Workflow Assigned</h3>
          <p className="text-muted-foreground text-sm">
            This case doesn't have a workflow assigned yet. Contact your
            case manager to set up a workflow for this case type.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
