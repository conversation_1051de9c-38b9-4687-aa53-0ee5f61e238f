import { Card, CardContent } from '@lilypad/ui/components/card';
import { Skeleton } from '@lilypad/ui/components/skeleton';

export function CaseWorkflowContainerSkeleton() {
  return (
    <Card className="mt-12 h-full shadow-none">
      <CardContent className="p-6">
        <div className="mb-4 flex items-center justify-between">
          <div>
            <Skeleton className="mb-2 h-6 w-48" />
            <Skeleton className="h-4 w-72" />
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-6 w-16" />
            <Skeleton className="h-6 w-20" />
          </div>
        </div>
        <Skeleton className="h-[calc(100vh-300px)]" />
      </CardContent>
    </Card>
  );
}
