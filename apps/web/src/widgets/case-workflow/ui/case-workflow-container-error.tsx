import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@lilypad/ui/components/alert';
import { AlertCircleIcon } from 'lucide-react';

interface CaseWorkflowContainerErrorProps {
  errorMessage: string;
}

export function CaseWorkflowContainerError({
  errorMessage,
}: CaseWorkflowContainerErrorProps) {
  return (
    <div className="flex h-[90vh] items-center justify-center p-8">
      <Alert className="max-w-md" variant="destructive">
        <AlertCircleIcon className="size-4" />
        <AlertTitle>Error Loading Workflow</AlertTitle>
        <AlertDescription>
          {errorMessage || 'Failed to load case workflow. Please try again.'}
        </AlertDescription>
      </Alert>
    </div>
  );
}
