'use client';

import type { TRPCClientError } from '@lilypad/api/client';
import { useTRPC } from '@lilypad/api/client';
import { useSuspenseQuery } from '@tanstack/react-query';
import { CaseWorkflowVisualization } from '@/features/case-management';
import { CaseWorkflowContainerError } from './case-workflow-container-error';
import { CaseWorkflowNotFound } from './case-workflow-not-found';

interface CaseWorkflowContainerProps {
  caseId: string;
}

export function CaseWorkflowContainer({ caseId }: CaseWorkflowContainerProps) {
  const trpc = useTRPC();

  const { data, isError, error } = useSuspenseQuery(
    trpc.cases.getCaseById.queryOptions(caseId, {
      staleTime: 1000 * 60 * 5, // 5 minutes
    })
  );

  if (isError) {
    const tRPCError = error as TRPCClientError;
    return <CaseWorkflowContainerError errorMessage={tRPCError.message} />;
  }

  if (!data?.workflow) {
    return <CaseWorkflowNotFound /> 
  }

  return <CaseWorkflowVisualization workflow={data.workflow} />;
}
