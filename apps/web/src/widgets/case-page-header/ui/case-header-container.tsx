'use client';

import { useTRPC } from '@lilypad/api/client';
import { Skeleton } from '@lilypad/ui/components/skeleton';
import { useSuspenseQuery } from '@tanstack/react-query';
import { CaseHeader } from '@/entities/cases';
import { AlertTriangleIcon } from 'lucide-react';

interface CaseHeaderContainerProps {
  caseId: string;
}

export function CasePageHeaderContainer({ caseId }: CaseHeaderContainerProps) {
  const trpc = useTRPC();

  const { data, isError } = useSuspenseQuery(
    trpc.cases.getCaseById.queryOptions(caseId, {
      staleTime: 1000 * 60 * 5, // 5 minutes
    })
  );

  if(!data) {
    return (
      <div className="flex h-full items-center gap-2">
        <AlertTriangleIcon className="size-4 text-yellow-500" />
        <p className="text-muted-foreground text-sm">Case not found.</p>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex h-full items-center gap-2">
        <AlertTriangleIcon className="size-4 text-red-500" />
        <p className="text-muted-foreground text-sm">
          Unable to load case details. Please try again.
        </p>
      </div>
    );
  }

  return <CaseHeader data={data} />;
}

export function CaseHeaderContainerSkeleton() {
  return (
    <div className="flex h-full items-center gap-6">

      <div className="flex items-center gap-3">
        <div>
          <Skeleton className="mb-1 h-5 w-24" />
          <Skeleton className="h-3 w-32" />
        </div>
        <div className="flex gap-1.5">
          <Skeleton className="h-5 w-14" />
          <Skeleton className="h-5 w-16" />
          <Skeleton className="h-5 w-12" />
        </div>
      </div>

      <Skeleton className="h-8 w-px" />

      <div className="flex items-center gap-3">
        <Skeleton className="size-8 rounded-full" />
        <div>
          <Skeleton className="mb-1 h-4 w-32" />
          <Skeleton className="h-3 w-40" />
        </div>
      </div>

      <Skeleton className="h-8 w-px" />

      <div className="flex items-center gap-4">
        <Skeleton className="h-10 w-20" />
        <Skeleton className="h-10 w-20" />
      </div>

      <Skeleton className="h-8 w-px" />

      <div className="ml-auto flex items-center gap-2">
        <Skeleton className="size-6 rounded-full" />
        <div className="-space-x-2 flex">
          <Skeleton className="size-7 rounded-full" />
          <Skeleton className="size-7 rounded-full" />
          <Skeleton className="size-7 rounded-full" />
          <Skeleton className="size-7 rounded-full" />
        </div>
      </div>
    </div>
  );
}
