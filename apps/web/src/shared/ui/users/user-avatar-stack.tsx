'use client';

import { Avatar, AvatarFallback } from '@lilypad/ui/components/avatar';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@lilypad/ui/components/tooltip';
import { cn } from '@lilypad/ui/lib/utils';
import { User } from 'lucide-react';

interface UserAvatarStackProps {
  userNames: string[];
  maxVisible?: number;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function UserAvatarStack({
  userNames,
  maxVisible = 3,
  size = 'md',
  className,
}: UserAvatarStackProps) {
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .slice(0, 2) // Take first two words (first and last name)
      .map((n) => n[0])
      .join('')
      .toUpperCase();
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'size-6 text-xs';
      case 'lg':
        return 'size-10 text-sm';
      default:
        return 'size-8 text-xs';
    }
  };

  if (userNames.length === 0) {
    return (
      <div
        className={cn(
          'flex items-center gap-2 text-muted-foreground',
          className
        )}
      >
        <User className="size-4" />
        <span className="text-sm">Unassigned</span>
      </div>
    );
  }

  const visibleUsers = userNames.slice(0, maxVisible);
  const remainingCount = userNames.length - maxVisible;

  return (
    <div className={cn('flex items-center', className)}>
      <div className="-space-x-2 flex">
        {visibleUsers.map((userName, index) => (
          <Tooltip key={`${userName}-${index}`}>
            <TooltipTrigger>
              <Avatar
                className={cn(
                  'border-2 border-background ring-1 ring-border',
                  getSizeClasses()
                )}
              >
                <AvatarFallback className="bg-gradient-to-br from-primary/10 to-primary/20 font-medium text-primary">
                  {getInitials(userName)}
                </AvatarFallback>
              </Avatar>
            </TooltipTrigger>
            <TooltipContent>
              <p>{userName}</p>
            </TooltipContent>
          </Tooltip>
        ))}

        {remainingCount > 0 && (
          <Tooltip>
            <TooltipTrigger>
              <Avatar
                className={cn(
                  'border-2 border-background ring-1 ring-border',
                  getSizeClasses()
                )}
              >
                <AvatarFallback className="bg-muted font-medium text-muted-foreground">
                  +{remainingCount}
                </AvatarFallback>
              </Avatar>
            </TooltipTrigger>
            <TooltipContent>
              <div className="space-y-1">
                {userNames.slice(maxVisible).map((name, index) => (
                  <p key={`${name}-${index}`}>{name}</p>
                ))}
              </div>
            </TooltipContent>
          </Tooltip>
        )}
      </div>
    </div>
  );
}
