'use client';

import NiceModal, { type NiceModalHocProps } from '@ebay/nice-modal-react';
import { MAX_IMAGE_SIZE } from '@lilypad/shared/file-upload';
import { Button } from '@lilypad/ui/components/button';
import { <PERSON><PERSON>per, type CropperElement } from '@lilypad/ui/components/cropper';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from '@lilypad/ui/components/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle
} from '@lilypad/ui/components/drawer';
import { toast } from '@lilypad/ui/components/sonner';
import { useMediaQuery } from '@lilypad/ui/hooks/use-media-query';
import { MediaQueries } from '@lilypad/ui/lib/media-queries';
import { cn } from '@lilypad/ui/lib/utils';
import { useRef } from 'react';
import { useEnhancedModal } from '@/shared/hooks/use-enhanced-modal';

export type CropPhotoModalProps = NiceModalHocProps & {
  file: File;
  aspectRatio?: number;
  circularCrop: boolean;
};

export const CropPhotoModal = NiceModal.create<CropPhotoModalProps>(
  ({ file, aspectRatio, circularCrop }) => {
    const modal = useEnhancedModal();
    const mdUp = useMediaQuery(MediaQueries.MdUp, { ssr: false });
    const title = 'Crop photo';
    const description = 'Adjust the size of the grid to crop your image.';
    const cropperRef = useRef<CropperElement>(null);

    const handleApply = async () => {
      if (cropperRef.current) {
        const croppedImage = await cropperRef.current.getCroppedImage();
        if (croppedImage) {
          modal.resolve(croppedImage);
          modal.handleClose();
        } else {
          toast.error('Failed to crop the image.');
        }
      }
    };

    const renderContent = (
      <div
        className={cn(
          'min-h-0 flex-1 overflow-y-auto',
          mdUp ? 'px-6 py-4' : 'p-4'
        )}
      >
        <Cropper
          ref={cropperRef}
          file={file}
          aspectRatio={aspectRatio}
          circularCrop={circularCrop}
          maxImageSize={MAX_IMAGE_SIZE}
        />
      </div>
    );

    const renderButtons = (
      <div
        className={cn(
          'flex flex-shrink-0 gap-2 bg-secondary p-4 md:justify-between md:rounded-b-md'
        )}
      >
        <Button
          type="button"
          variant="outline"
          size="sm"
          className="w-1/2 md:w-auto"
          onClick={modal.handleClose}
        >
          Cancel
        </Button>
        <Button
          type="button"
          variant="default"
          size="sm"
          className="w-1/2 md:w-auto"
          onClick={handleApply}
        >
          Apply
        </Button>
      </div>
    );

    return (
      <>
        {mdUp ? (
          <Dialog open={modal.visible} onOpenChange={modal.handleOpenChange}>
            <DialogContent
              className="flex max-w-lg flex-col gap-0 p-0"
              onAnimationEndCapture={modal.handleAnimationEndCapture}
            >
              <DialogHeader className="flex-shrink-0 border-b px-6 py-5">
                <DialogTitle>{title}</DialogTitle>
                <DialogDescription>{description}</DialogDescription>
              </DialogHeader>
              {renderContent}
              {renderButtons}
            </DialogContent>
          </Dialog>
        ) : (
          <Drawer
            open={modal.visible}
            onOpenChange={modal.handleOpenChange}
          >
            <DrawerContent className="flex flex-col">
              <DrawerHeader className="flex-shrink-0 border-b text-left">
                <DrawerTitle>{title}</DrawerTitle>
                <DrawerDescription>{description}</DrawerDescription>
              </DrawerHeader>
              {renderContent}
              {renderButtons}
            </DrawerContent>
          </Drawer>
        )}
      </>
    );
  }
);
