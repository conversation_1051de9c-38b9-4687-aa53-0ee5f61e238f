import {
  CaseWorkflowStatusEnum,
  CaseWorkflowStatusEnumMap,
} from '@lilypad/db/enums';
import { Badge } from '@lilypad/ui/components/badge';
import { cn } from '@lilypad/ui/lib/utils';

interface CaseWorkflowStepStatusBadgeProps {
  status: CaseWorkflowStatusEnum;
  size?: 'sm' | 'default' | 'lg';
  className?: string;
}

const STATUS_STYLES = {
  [CaseWorkflowStatusEnum.COMPLETED]: {
    variant: 'default' as const,
    className:
      'bg-green-100 text-green-800 border-green-200 hover:bg-green-200 dark:bg-green-950/20 dark:text-green-400 dark:border-green-800',
  },
  [CaseWorkflowStatusEnum.IN_PROGRESS]: {
    variant: 'secondary' as const,
    className:
      'bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200 dark:bg-blue-950/20 dark:text-blue-400 dark:border-blue-800',
  },
  [CaseWorkflowStatusEnum.SKIPPED]: {
    variant: 'outline' as const,
    className:
      'bg-gray-100 text-gray-600 border-gray-300 hover:bg-gray-200 dark:bg-gray-950/20 dark:text-gray-400 dark:border-gray-700',
  },
  [CaseWorkflowStatusEnum.PENDING]: {
    variant: 'outline' as const,
    className:
      'bg-gray-50 text-gray-600 border-gray-200 hover:bg-gray-100 dark:bg-gray-950/10 dark:text-gray-400 dark:border-gray-800',
  },
} as const;

export function CaseWorkflowStepStatusBadge({
  status,
  size = 'default',
  className,
}: CaseWorkflowStepStatusBadgeProps) {
  const statusConfig =
    STATUS_STYLES[status] || STATUS_STYLES[CaseWorkflowStatusEnum.PENDING];
  const displayText = CaseWorkflowStatusEnumMap[status] || status;

  return (
    <Badge
      className={cn(statusConfig.className, className)}
      size={size}
      variant={statusConfig.variant}
    >
      {displayText}
    </Badge>
  );
}
