'use client';

import { useTRPC } from '@lilypad/api/client';
import {
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import { useQuery } from '@tanstack/react-query';
import { useFormContext } from 'react-hook-form';
import { LanguageMultiselect } from './language-multi-select';

interface LanguageMultiSelectFieldProps {
  languageIdsFieldName: string;
  primaryLanguageIdFieldName: string;
  label?: string;
}

export function LanguageMultiSelectField({
  languageIdsFieldName,
  primaryLanguageIdFieldName,
  label = 'Languages',
}: LanguageMultiSelectFieldProps) {
  const form = useFormContext();
  const trpc = useTRPC();

  const { data: languages = [] } = useQuery(
    trpc.languages.getAllLanguages.queryOptions()
  );

  const selectedLanguageIds = form.watch(languageIdsFieldName) || [];
  const primaryLanguageId = form.watch(primaryLanguageIdFieldName);

  const handleLanguageChange = (
    languageIds: string[],
    newPrimaryLanguageId: string | null
  ) => {
    form.setValue(languageIdsFieldName, languageIds);
    form.setValue(primaryLanguageIdFieldName, newPrimaryLanguageId);
  };

  return (
    <FormField
      control={form.control}
      name={languageIdsFieldName}
      render={({ fieldState }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <LanguageMultiselect
            hasError={!!fieldState.error}
            languages={languages}
            onChange={handleLanguageChange}
            primaryLanguageId={primaryLanguageId}
            value={selectedLanguageIds}
          />
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
