'use client';

import { useTRPC } from '@lilypad/api/client';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@lilypad/ui/components/select';
import { useQuery } from '@tanstack/react-query';
import { useFormContext } from 'react-hook-form';

interface SchoolSelectProps {
  fieldName: string;
  label?: string;
  placeholder?: string;
  required?: boolean;
}

export function SchoolSelect({
  fieldName,
  label = 'School',
  placeholder = 'Select a school',
}: SchoolSelectProps) {
  const form = useFormContext();
  const trpc = useTRPC();

  const { data: schools } = useQuery(trpc.schools.getSchools.queryOptions());

  return (
    <FormField
      control={form.control}
      name={fieldName}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <Select onValueChange={field.onChange} value={field.value}>
            <FormControl>
              <SelectTrigger className="w-full">
                <SelectValue placeholder={placeholder} />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {schools?.map((school) => (
                <SelectItem key={school.id} value={school.id}>
                  {school.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
