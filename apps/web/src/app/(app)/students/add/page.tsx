import { HydrateClient, prefetch, trpc } from '@lilypad/api/server';
import { routes } from '@lilypad/shared/routes';
import { DynamicBreadcrumb } from '@lilypad/ui/components/dynamic-breadcrumb';
import {
  Page,
  PageBody,
  PageHeader,
  PagePrimaryBar,
} from '@lilypad/ui/components/page';
import type { Metadata } from 'next';
import Link from 'next/link';
import { Suspense } from 'react';
import { AddStudentForm } from '@/features/add-student/ui/add-student-form';
import { AddStudentFormSkeleton } from '@/features/add-student/ui/add-student-form-skeleton';
import { CREATE_STUDENT_PAGE_METADATA } from '../../metadata';

export const metadata: Metadata = CREATE_STUDENT_PAGE_METADATA;

export default function CreateStudentPage() {
  prefetch(trpc.languages.getAllLanguages.queryOptions());
  prefetch(trpc.schools.getSchools.queryOptions());

  const breadcrumbItems = [
    {
      label: 'Students',
      href: routes.app.students.Index,
      asChild: true,
    },
    {
      label: 'Add Student/Referral',
    },
  ];

  return (
    <Page>
      <PageHeader>
        <PagePrimaryBar>
          <DynamicBreadcrumb
            items={breadcrumbItems}
            linkComponent={({ href, children }) => (
              <Link href={href}>{children}</Link>
            )}
          />
        </PagePrimaryBar>
      </PageHeader>
      <PageBody disableScroll>
        <HydrateClient>
          <Suspense fallback={<AddStudentFormSkeleton />}>
            <AddStudentForm />
          </Suspense>
        </HydrateClient>
      </PageBody>
    </Page>
  );
}