import { HydrateClient, prefetch, trpc } from '@lilypad/api/server';
import { routes } from '@lilypad/shared/routes';
import { DynamicBreadcrumb } from '@lilypad/ui/components/dynamic-breadcrumb';
import {
  Page,
  PageBody,
  PageHeader,
  PagePrimaryBar,
} from '@lilypad/ui/components/page';
import Link from 'next/link';
import { Suspense } from 'react';
import {
  CreateStudentsTable,
  CreateStudentsTableSkeleton,
} from '@/widgets/add-students-table';

export default async function AddStudentPage() {
  prefetch(trpc.languages.getAllLanguages.queryOptions());
  prefetch(trpc.schools.getSchools.queryOptions());

  const breadcrumbItems = [
    {
      label: 'Students',
      href: routes.app.students.Index,
      asChild: true,
    },
    {
      label: 'Add Students/Referrals',
    },
  ];
  return (
    <Page className="flex h-screen flex-col">
      <PageHeader className="flex-none">
        <PagePrimaryBar>
          <DynamicBreadcrumb
            items={breadcrumbItems}
            linkComponent={({ href, children }) => (
              <Link href={href}>{children}</Link>
            )}
          />
        </PagePrimaryBar>
      </PageHeader>
      <PageBody disableScroll className="flex-1 overflow-hidden">
        <HydrateClient>
          <Suspense fallback={<CreateStudentsTableSkeleton />}>
            <CreateStudentsTable />
          </Suspense>
        </HydrateClient>
      </PageBody>
    </Page>
  );
}
