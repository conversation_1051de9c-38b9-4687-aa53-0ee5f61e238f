import { HydrateClient, prefetch, trpc } from '@lilypad/api/server';
import { Suspense } from 'react';
import { DistrictLogoCard } from '@/features/district-settings/ui/district-logo-card';
import { DistrictLogoSkeletonCard } from '@/features/district-settings/ui/district-logo-skeleton-card';

export default function DistrictLogoPage(): React.JSX.Element {
  prefetch(trpc.districts.getDistrictLogo.queryOptions());

  return (
    <HydrateClient>
      <Suspense fallback={<DistrictLogoSkeletonCard />}>
        <DistrictLogoCard />
      </Suspense>
    </HydrateClient>
  );
}

