import { HydrateClient, prefetch, trpc } from '@lilypad/api/server';
import { Suspense } from 'react';
import { DistrictPreferencesCard } from '@/features/district-settings/ui/district-preferences-card';
import { DistrictPreferencesSkeletonCard } from '@/features/district-settings/ui/district-preferences-skeleton-card';

export default function DistrictPreferencesPage(): React.JSX.Element {
  prefetch(trpc.districts.getDistrictPreferences.queryOptions());

  return (
    <HydrateClient>
      <Suspense fallback={<DistrictPreferencesSkeletonCard />}>
        <DistrictPreferencesCard />
      </Suspense>
    </HydrateClient>
  );
} 