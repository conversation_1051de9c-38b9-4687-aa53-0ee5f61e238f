import { RoleEnum } from '@lilypad/db/schema';
import { logger } from '@lilypad/shared/logger';
import { routes } from '@lilypad/shared/routes';
import { AnnotatedLayout } from '@lilypad/ui/components/annotated';
import { DynamicBreadcrumb } from '@lilypad/ui/components/dynamic-breadcrumb';
import {
  Page,
  PageBody,
  PageHeader,
  PagePrimaryBar,
  PageTitle
} from '@lilypad/ui/components/page';
import { Separator } from '@lilypad/ui/components/separator';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import { getAuthContext } from '@/shared/context';

const breadcrumbItems = [
  {
    label: 'Settings',
    href: routes.app.settings.Index,
    asChild: true,
  },
  {
    label: 'District',
  },
];


export type OrganizationGeneralLayoutProps = {
  districtLogo: React.ReactNode;
  districtDetails: React.ReactNode;
  districtAddress: React.ReactNode;
  districtPreferences: React.ReactNode;
  districtAvailabilities: React.ReactNode;
};

export default async function OrganizationGeneralLayout({
  districtLogo,
  districtDetails,
  districtAddress,
  districtPreferences,
  districtAvailabilities,
}: OrganizationGeneralLayoutProps): Promise<React.JSX.Element> {
  const userRole = await getUserRole();

  const allowedRoles = [RoleEnum.SPECIAL_ED_DIRECTOR, RoleEnum.SCHOOL_COORDINATOR, RoleEnum.SUPER_USER];
  if (!allowedRoles.includes(userRole)) {
    return notFound();
  }

  return (
    <Page>
      <PageHeader>
        <PagePrimaryBar>
          <PageTitle>
            <DynamicBreadcrumb
              items={breadcrumbItems}
              linkComponent={({ href, children }) => (
                <Link href={href}>{children}</Link>
              )}
            />
          </PageTitle>
        </PagePrimaryBar>
      </PageHeader>
      <PageBody disableScroll>
        <AnnotatedLayout>
          {districtLogo}
          <Separator />
          {districtDetails}
          <Separator />
          {districtAddress}
          <Separator />
          {districtAvailabilities}
          <Separator />
          {districtPreferences}
        </AnnotatedLayout>
      </PageBody>
    </Page>
  );
}

async function getUserRole() {
  const { user } = await getAuthContext();

  if (!user?.rolePermissions) {
    logger.warn(
      { userId: user?.id },
      'Dashboard access attempt without role permissions structure'
    );
    notFound();
  }

  if (!user.rolePermissions.roles?.length) {
    logger.warn(
      { userId: user.id, email: user.email },
      'Dashboard access attempt by user with no assigned roles'
    );
    notFound();
  }

  const userRole = user.rolePermissions.roles.map(role => role.name).at(0) as RoleEnum;
  return userRole;
}