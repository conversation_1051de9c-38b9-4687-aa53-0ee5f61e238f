import { HydrateClient, prefetch, trpc } from '@lilypad/api/server';
import { Suspense } from 'react';
import { DistrictDetailsCard } from '@/features/district-settings/ui/district-details-card';
import { DistrictDetailsSkeletonCard } from '@/features/district-settings/ui/district-details-skeleton-card';

export default function DistrictDetailsPage(): React.JSX.Element {
  prefetch(trpc.districts.getDistrictDetails.queryOptions());

  return (
    <HydrateClient>
      <Suspense fallback={<DistrictDetailsSkeletonCard />}>
        <DistrictDetailsCard />
      </Suspense>
    </HydrateClient>
  );
}
