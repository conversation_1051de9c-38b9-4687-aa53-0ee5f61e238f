import { HydrateClient, prefetch, trpc } from '@lilypad/api/server';
import { Suspense } from 'react';
import { DistrictAvailabilitiesCard } from '@/features/district-settings/ui/district-availabilities-card';
import { DistrictAvailabilitiesSkeletonCard } from '@/features/district-settings/ui/district-availabilities-skeleton-card';

export default function DistrictAvailabilitiesPage(): React.JSX.Element {
  prefetch(trpc.districts.getDistrictAvailabilities.queryOptions());

  return (
    <HydrateClient>
      <Suspense fallback={<DistrictAvailabilitiesSkeletonCard />}>
        <DistrictAvailabilitiesCard />
      </Suspense>
    </HydrateClient>
  );
} 