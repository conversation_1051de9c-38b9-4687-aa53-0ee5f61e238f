import { HydrateClient, prefetch, trpc } from '@lilypad/api/server';
import { Suspense } from 'react';
import { DistrictAddressCard } from '@/features/district-settings/ui/district-address-card';
import { DistrictAddressSkeletonCard } from '@/features/district-settings/ui/district-address-skeleton-card';

export default function DistrictAddressPage(): React.JSX.Element {
  prefetch(trpc.districts.getDistrictAddress.queryOptions());

  return (
    <HydrateClient>
      <Suspense fallback={<DistrictAddressSkeletonCard />}>
        <DistrictAddressCard />
      </Suspense>
    </HydrateClient>
  );
} 