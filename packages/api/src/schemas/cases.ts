import {
  CasePriorityEnum,
  CaseStatusEnum,
  CaseTypeEnum,
} from '@lilypad/db/enums';
import { z } from 'zod';

export const getCasesInputSchema = z.object({
  page: z.number().min(1).optional(),
  perPage: z.number().min(1).max(100).optional(),
  search: z.string().optional(),
  filters: z
    .array(
      z.object({
        id: z.string(),
        value: z.unknown(),
        isMulti: z.boolean().optional(),
      })
    )
    .optional(),
  joinOperator: z.enum(['and', 'or']).optional(),
  sort: z
    .array(
      z.object({
        id: z.string(),
        desc: z.boolean(),
      })
    )
    .optional(),
});

export const caseFilterSchema = z.object({
  status: z.nativeEnum(CaseStatusEnum).optional(),
  priority: z.nativeEnum(CasePriorityEnum).optional(),
  caseType: z.nativeEnum(CaseTypeEnum).optional(),
  studentName: z.string().optional(),
  isActive: z.boolean().optional(),
  evaluationDueDate: z.date().optional(),
  meetingDate: z.date().optional(),
});

// Type exports
export type GetCasesInput = z.infer<typeof getCasesInputSchema>;
export type CaseFilter = z.infer<typeof caseFilterSchema>;
