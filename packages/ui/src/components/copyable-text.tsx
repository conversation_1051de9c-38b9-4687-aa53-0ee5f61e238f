import type { PropsWithChildren } from 'react';
import { CopyButton } from './copy-button';
import { If } from './if';

interface CopyableTextProps extends PropsWithChildren {
  value: string;
  label?: string;
  className?: string;
}

export function CopyableText({ value, label, children }: CopyableTextProps) {
  return (
    <div className="flex justify-between">
      <If condition={label}>
        <span className="text-muted-foreground">{label}</span>
      </If>
      <div className="group flex items-center space-x-1">
        {children ? children : <span>{value}</span>}
        <CopyButton value={value} />
      </div>
    </div>
  );
};
