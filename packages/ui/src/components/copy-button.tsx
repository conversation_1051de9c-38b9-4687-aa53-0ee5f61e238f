'use client';

import { Button, type ButtonProps } from '@lilypad/ui/components/button';
import { cn } from '@lilypad/ui/lib/utils';
import { CheckIcon, ClipboardIcon } from 'lucide-react';
import { useEffect, useState } from 'react';

interface CopyButtonProps extends ButtonProps {
  value: string;
  src?: string;
}

export function CopyButton({
  value,
  className,
  src,
  variant = 'ghost',
  ...props
}: CopyButtonProps) {
  const [hasCopied, setHasCopied] = useState(false);

  useEffect(() => {
    setTimeout(() => {
      setHasCopied(false);
    }, 2000);
  }, []);

  function copyToClipboard(val: string) {
    navigator.clipboard.writeText(val);
    setHasCopied(true);
  }

  return (
    <Button
      {...props}
      className={cn(
        'relative z-10 h-6 w-6 opacity-0 transition-all duration-200 ease-in-out hover:opacity-100 group-hover:opacity-100 [&_svg]:h-3 [&_svg]:w-3 ',
        className
      )}
      onClick={() => copyToClipboard(value)}
      size="icon"
      variant="ghost"
    >
      <span className="sr-only">Copy</span>
      {hasCopied ? (
        <CheckIcon className="text-green-700" />
      ) : (
        <ClipboardIcon className="text-muted-foreground" />
      )}
    </Button>
  );
}
