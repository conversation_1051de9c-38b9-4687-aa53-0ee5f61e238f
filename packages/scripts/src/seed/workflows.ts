import { createDatabaseClient } from '@lilypad/db/client';
import {
  type NewStepDependency,
  type NewWorkflow,
  type NewWorkflowStep,
  stepDependenciesTable,
  workflowStepsTable,
  workflowsTable,
} from '@lilypad/db/schema';
import { logger } from '@lilypad/shared/logger';
import { IEP_WORKFLOW, IEP_WORKFLOW_STEPS } from '../data/workflows';


/*
 * -----------------------------------------------------------------------------
 * SECTION: Helper Functions
 * -----------------------------------------------------------------------------
 */

async function createWorkflow(): Promise<{
  workflowId: string;
  stepIds: { stepNumber: number; id: string }[];
}> {
  const db = await createDatabaseClient({ admin: true });

  return await db.transaction(async (tx) => {
    // 1. Create the workflow
    logger.info('Creating IEP Evaluation workflow...');

    const workflowData: NewWorkflow = {
      name: IEP_WORKFLOW.name,
      description: IEP_WORKFLOW.description,
      version: IEP_WORKFLOW.version,
      isActive: IEP_WORKFLOW.isActive,
      createdAt: new Date(),
    };

    const [workflow] = await tx
      .insert(workflowsTable)
      .values(workflowData)
      .returning({ id: workflowsTable.id });

    if (!workflow) {
      throw new Error('Failed to create IEP workflow');
    }

    logger.info({ workflowId: workflow.id }, 'IEP Evaluation workflow created');

    // 2. Create workflow steps
    logger.info('Creating workflow steps...');

    const stepData: NewWorkflowStep[] = IEP_WORKFLOW_STEPS.map((step) => ({
      workflowId: workflow.id,
      stepNumber: step.stepNumber,
      name: step.name,
      description: step.description,
      estimatedDays: step.estimatedDays,
      isOptional: step.isOptional,
      createdAt: new Date(),
    }));

    const insertedSteps = await tx
      .insert(workflowStepsTable)
      .values(stepData)
      .returning({
        id: workflowStepsTable.id,
        stepNumber: workflowStepsTable.stepNumber,
      });

    if (insertedSteps.length !== IEP_WORKFLOW_STEPS.length) {
      throw new Error('Failed to create all workflow steps');
    }

    logger.info(
      { stepCount: insertedSteps.length },
      'Workflow steps created successfully'
    );

    // 3. Create step dependencies (sequential: 1→2→3→...→10)
    logger.info('Creating step dependencies...');

    const dependencyData: NewStepDependency[] = [];

    // Create sequential dependencies: each step depends on the previous step
    for (let i = 1; i < insertedSteps.length; i++) {
      const currentStep = insertedSteps.find(
        (step) => step.stepNumber === i + 1
      );
      const previousStep = insertedSteps.find((step) => step.stepNumber === i);

      if (currentStep && previousStep) {
        dependencyData.push({
          stepId: currentStep.id,
          dependsOnStepId: previousStep.id,
          createdAt: new Date(),
        });
      }
    }

    if (dependencyData.length > 0) {
      await tx.insert(stepDependenciesTable).values(dependencyData);
      logger.info(
        { dependencyCount: dependencyData.length },
        'Step dependencies created successfully'
      );
    }


    const sortedSteps = insertedSteps.sort(
      (a, b) => a.stepNumber - b.stepNumber
    );

    return {
      workflowId: workflow.id,
      stepIds: sortedSteps.map((step) => ({
        stepNumber: step.stepNumber,
        id: step.id,
      })),
    };
  });
}

/*
 * -----------------------------------------------------------------------------
 * SECTION: Main Function
 * -----------------------------------------------------------------------------
 */

export async function seedWorkflows() {
  const startTime = Date.now();

  logger.info('Starting workflow seeding process...');

  try {
    const { workflowId, stepIds } = await createWorkflow();

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    logger.info(
      {
        workflowId,
        stepCount: stepIds.length,
        duration: `${duration}s`,
      },
      'Workflow seeding completed successfully'
    );

    return {
      workflowId,
      stepIds,
    };
  } catch (error) {
    logger.error({ error }, 'Fatal error in workflow seeding');
    throw error;
  }
}

async function main() {
  try {
    await seedWorkflows();
    logger.info('Workflow seeding script completed successfully');
  } catch (error) {
    logger.error({ error }, 'Fatal error in workflow seeding script');
    process.exit(1);
  }
}

main();
