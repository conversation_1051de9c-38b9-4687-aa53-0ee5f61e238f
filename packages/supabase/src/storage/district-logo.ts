import type { Supabase } from '../types/index';

import { extractFilenameFromUrl, generateBucketFilename } from './utils';

interface UploadDistrictLogoParams {
  supabase: Supabase;
  districtId: string;
  logoFile: File;
}

export async function uploadDistrictLogo({
  supabase,
  districtId,
  logoFile,
}: UploadDistrictLogoParams) {
  const bytes = await logoFile.arrayBuffer();
  const bucket = supabase.storage.from('logos');
  const fileName = generateBucketFilename(logoFile.name, districtId);

  const result = await bucket.upload(fileName, bytes, {
    contentType: logoFile.type,
  });

  if (result.error) {
    throw result.error;
  }

  return bucket.getPublicUrl(fileName).data.publicUrl;
}

export async function deleteDistrictLogo({
  supabase,
  publicUrl,
}: {
  supabase: Supabase;
  publicUrl: string;
}) {
  const bucket = supabase.storage.from('logos');
  const fileName = extractFilenameFromUrl(publicUrl);
  const result = await bucket.remove([fileName]);
  return result;
}
